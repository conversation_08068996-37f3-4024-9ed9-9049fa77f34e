<!-- JavaScript Utilities and Base Functions -->
<script>
// Global JavaScript utilities and functions
(function() {
    'use strict';
    
    // Enhanced UI Utilities
    window.UI = {
        // Toast notifications
        showToast: function(message, type = 'info', duration = 3000) {
            const toast = document.createElement('div');
            toast.className = `alert alert-${type} toast-notification`;
            toast.innerHTML = `
                <span>${message}</span>
                <button class="alert-close" onclick="this.parentElement.remove()">×</button>
            `;
            
            // Add toast styles
            toast.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 9999;
                min-width: 300px;
                animation: slideInRight 0.3s ease-out;
            `;
            
            document.body.appendChild(toast);
            
            // Auto remove after duration
            setTimeout(() => {
                if (toast.parentElement) {
                    toast.style.animation = 'slideOutRight 0.3s ease-in';
                    setTimeout(() => toast.remove(), 300);
                }
            }, duration);
        },
        
        // Loading overlay
        showLoadingOverlay: function(target = document.body) {
            const overlay = document.createElement('div');
            overlay.className = 'loading-overlay';
            overlay.innerHTML = `
                <div class="loading-content">
                    <div class="spinner-border text-primary"></div>
                    <p>Loading...</p>
                </div>
            `;
            overlay.style.cssText = `
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(255, 255, 255, 0.9);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 9998;
            `;
            
            if (target === document.body) {
                overlay.style.position = 'fixed';
            }
            
            target.style.position = 'relative';
            target.appendChild(overlay);
            return overlay;
        },
        
        hideLoadingOverlay: function(target = document.body) {
            const overlay = target.querySelector('.loading-overlay');
            if (overlay) {
                overlay.remove();
            }
        },
        
        // Confirm dialog
        confirm: function(message, callback) {
            const modal = document.createElement('div');
            modal.className = 'modal-overlay';
            modal.innerHTML = `
                <div class="modal-content">
                    <h3>Konfirmo</h3>
                    <p>${message}</p>
                    <div class="modal-actions">
                        <button class="btn btn-outline" onclick="this.closest('.modal-overlay').remove()">Anulo</button>
                        <button class="btn btn-danger" onclick="confirmAction()">Konfirmo</button>
                    </div>
                </div>
            `;
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0, 0, 0, 0.5);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 10000;
            `;
            
            window.confirmAction = function() {
                callback();
                modal.remove();
                delete window.confirmAction;
            };
            
            document.body.appendChild(modal);
        }
    };
    
    // Global utility functions
    window.showLoading = function(elementId) {
        const element = document.getElementById(elementId);
        if (element) {
            element.innerHTML = '<div class="loading"><div class="spinner"></div><p>Loading...</p></div>';
        }
    };
    
    window.showError = function(elementId, message) {
        const element = document.getElementById(elementId);
        if (element) {
            element.innerHTML = `<div class="alert alert-error">${message}</div>`;
        }
    };
    
    window.showSuccess = function(elementId, message) {
        const element = document.getElementById(elementId);
        if (element) {
            element.innerHTML = `<div class="alert alert-success">${message}</div>`;
        }
    };
    
    // Enhanced logout with confirmation
    window.logout = async function() {
        UI.confirm('Jeni të sigurt që dëshironi të dilni?', async function() {
            const overlay = UI.showLoadingOverlay();
            
            try {
                const response = await fetch('/api/auth/logout/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': getCookie('csrftoken')
                    }
                });
                
                if (response.ok) {
                    UI.showToast('Ju keni dalë me sukses', 'success');
                    setTimeout(() => {
                        window.location.href = '/auth/login/';
                    }, 1000);
                } else {
                    throw new Error('Logout failed');
                }
            } catch (error) {
                UI.hideLoadingOverlay();
                UI.showToast('Gabim gjatë daljes: ' + error.message, 'error');
            }
        });
    };
    
    // Cookie utility
    window.getCookie = function(name) {
        let cookieValue = null;
        if (document.cookie && document.cookie !== '') {
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.substring(0, name.length + 1) === (name + '=')) {
                    cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                    break;
                }
            }
        }
        return cookieValue;
    };
    
    // Initialize tooltips
    function initializeTooltips() {
        // Add tooltip functionality to elements with data-tooltip attribute
        document.querySelectorAll('[data-tooltip]').forEach(element => {
            element.classList.add('tooltip');
        });
    }
    
    // Add dynamic CSS animations
    function addDynamicStyles() {
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideInRight {
                from {
                    opacity: 0;
                    transform: translateX(100%);
                }
                to {
                    opacity: 1;
                    transform: translateX(0);
                }
            }
            
            @keyframes slideOutRight {
                from {
                    opacity: 1;
                    transform: translateX(0);
                }
                to {
                    opacity: 0;
                    transform: translateX(100%);
                }
            }
            
            .modal-content {
                background: white;
                padding: 2rem;
                border-radius: 12px;
                box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
                max-width: 400px;
                width: 90%;
                animation: modalSlideIn 0.3s ease-out;
            }
            
            @keyframes modalSlideIn {
                from {
                    opacity: 0;
                    transform: scale(0.8) translateY(-20px);
                }
                to {
                    opacity: 1;
                    transform: scale(1) translateY(0);
                }
            }
            
            .modal-actions {
                display: flex;
                gap: 1rem;
                justify-content: flex-end;
                margin-top: 1.5rem;
            }
            
            .loading-content {
                text-align: center;
                color: var(--primary-color);
            }
            
            .loading-content p {
                margin-top: 1rem;
                font-weight: 500;
            }
        `;
        document.head.appendChild(style);
    }
    
    // Setup keyboard shortcuts
    function setupKeyboardShortcuts() {
        document.addEventListener('keydown', function(e) {
            // ESC key closes modals and dropdowns
            if (e.key === 'Escape') {
                document.querySelectorAll('.modal-overlay').forEach(modal => modal.remove());
                document.querySelectorAll('.user-dropdown.show').forEach(dropdown => {
                    dropdown.classList.remove('show');
                });
            }
            
            // Ctrl+K opens search
            if (e.ctrlKey && e.key === 'k') {
                e.preventDefault();
                const searchInput = document.getElementById('globalSearch');
                if (searchInput) {
                    searchInput.focus();
                }
            }
        });
    }
    
    // Initialize base functionality when DOM is loaded
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize tooltips
        initializeTooltips();
        
        // Add CSS animations
        addDynamicStyles();
        
        // Setup keyboard shortcuts
        setupKeyboardShortcuts();
        
        // Add smooth scrolling to anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    });
    
})();
</script>
