{% extends 'base.html' %}

{% block title %}Register - Dental Lab Management System{% endblock %}

{% block content %}
<div style="max-width: 600px; margin: 0 auto;">
    <h2 style="text-align: center; margin-bottom: 30px; color: #2c3e50;">Register Your Dental Clinic</h2>
    
    <div id="registerMessages"></div>
    
    <form id="registerForm">
        <div class="two-column">
            <div class="form-group">
                <label for="first_name">First Name</label>
                <input type="text" id="first_name" name="first_name" required>
            </div>
            
            <div class="form-group">
                <label for="last_name">Last Name</label>
                <input type="text" id="last_name" name="last_name" required>
            </div>
        </div>
        
        <div class="form-group">
            <label for="email">Email Address</label>
            <input type="email" id="email" name="email" required>
        </div>
        
        <div class="two-column">
            <div class="form-group">
                <label for="password">Password</label>
                <input type="password" id="password" name="password" required>
            </div>
            
            <div class="form-group">
                <label for="password_confirm">Confirm Password</label>
                <input type="password" id="password_confirm" name="password_confirm" required>
            </div>
        </div>
        
        <hr style="margin: 30px 0; border: none; border-top: 1px solid #eee;">
        <h3 style="color: #2c3e50; margin-bottom: 20px;">Clinic Information</h3>
        
        <div class="form-group">
            <label for="clinic_name">Clinic Name</label>
            <input type="text" id="clinic_name" name="clinic_name" required>
        </div>
        
        <div class="two-column">
            <div class="form-group">
                <label for="nipt">NIPT</label>
                <input type="text" id="nipt" name="nipt" required>
            </div>
            
            <div class="form-group">
                <label for="phone_number">Phone Number</label>
                <input type="tel" id="phone_number" name="phone_number" required>
            </div>
        </div>
        
        <div class="form-group">
            <label for="contact_person">Contact Person</label>
            <input type="text" id="contact_person" name="contact_person" required>
        </div>
        
        <div class="form-group">
            <label for="address">Address</label>
            <textarea id="address" name="address" rows="3" required></textarea>
        </div>
        
        <div style="text-align: center;">
            <button type="submit" class="btn" style="width: 100%;">Register Clinic</button>
        </div>
    </form>
    
    <div style="text-align: center; margin-top: 20px;">
        <p>Already have an account? <a href="/auth/login/" style="color: #3498db;">Login here</a></p>
    </div>
    
    <div style="margin-top: 30px; padding: 20px; background: #fff3cd; border-radius: 5px; border-left: 4px solid #ffc107;">
        <h4 style="color: #856404; margin-bottom: 10px;">Registration Process</h4>
        <ol style="color: #856404; margin-left: 20px;">
            <li>Fill out the registration form with your clinic details</li>
            <li>Submit the form and wait for admin approval</li>
            <li>You will receive an email once your account is activated</li>
            <li>Login with your credentials to start using the system</li>
        </ol>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.getElementById('registerForm').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    // Get form data
    const formData = new FormData(this);
    const data = Object.fromEntries(formData.entries());
    
    // Basic validation
    if (data.password !== data.password_confirm) {
        showError('registerMessages', 'Passwords do not match');
        return;
    }
    
    showLoading('registerMessages');
    
    try {
        const response = await fetch('/api/auth/register/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCookie('csrftoken')
            },
            body: JSON.stringify(data)
        });
        
        const responseData = await response.json();
        
        if (response.ok) {
            showSuccess('registerMessages', `
                <strong>Registration Successful!</strong><br>
                Your account has been created and is pending admin approval.<br>
                You will receive an email once your account is activated.<br>
                <a href="/auth/login/" style="color: #155724;">Go to Login</a>
            `);
            
            // Clear form
            document.getElementById('registerForm').reset();
        } else {
            let errorMessage = 'Registration failed';
            
            if (responseData.non_field_errors) {
                errorMessage = responseData.non_field_errors[0];
            } else {
                // Collect all field errors
                const errors = [];
                for (const [field, fieldErrors] of Object.entries(responseData)) {
                    if (Array.isArray(fieldErrors)) {
                        errors.push(`${field}: ${fieldErrors[0]}`);
                    }
                }
                if (errors.length > 0) {
                    errorMessage = errors.join('<br>');
                }
            }
            
            showError('registerMessages', errorMessage);
        }
    } catch (error) {
        showError('registerMessages', 'Network error. Please try again.');
        console.error('Registration error:', error);
    }
});

// Password strength indicator
document.getElementById('password').addEventListener('input', function() {
    const password = this.value;
    const strength = checkPasswordStrength(password);
    
    // You can add password strength indicator here
});

function checkPasswordStrength(password) {
    let score = 0;
    if (password.length >= 8) score++;
    if (/[A-Z]/.test(password)) score++;
    if (/[a-z]/.test(password)) score++;
    if (/[0-9]/.test(password)) score++;
    if (/[^A-Za-z0-9]/.test(password)) score++;
    
    return score;
}

// Real-time password confirmation check
document.getElementById('password_confirm').addEventListener('input', function() {
    const password = document.getElementById('password').value;
    const confirmPassword = this.value;
    
    if (confirmPassword && password !== confirmPassword) {
        this.style.borderColor = '#e74c3c';
    } else {
        this.style.borderColor = '#ddd';
    }
});
</script>
{% endblock %}
