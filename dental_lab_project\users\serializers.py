from rest_framework import serializers
from django.contrib.auth import authenticate
from django.contrib.auth.password_validation import validate_password
from .models import CustomUser, ClinicProfile, TechnicianProfile


class UserRegistrationSerializer(serializers.ModelSerializer):
    """Serializer for user registration."""
    
    password = serializers.CharField(write_only=True, validators=[validate_password])
    password_confirm = serializers.CharField(write_only=True)
    
    # Clinic specific fields
    clinic_name = serializers.CharField(max_length=255, required=False)
    nipt = serializers.Char<PERSON>ield(max_length=20, required=False)
    address = serializers.Char<PERSON>ield(style={'base_template': 'textarea.html'}, required=False)
    contact_person = serializers.CharField(max_length=255, required=False)
    phone_number = serializers.Char<PERSON>ield(max_length=20, required=False)
    
    class Meta:
        model = CustomUser
        fields = [
            'email', 'first_name', 'last_name', 'password', 'password_confirm',
            'clinic_name', 'nipt', 'address', 'contact_person', 'phone_number'
        ]
    
    def validate(self, attrs):
        """Validate password confirmation and clinic data."""
        if attrs['password'] != attrs['password_confirm']:
            raise serializers.ValidationError("Passwords don't match.")
        
        # Remove password_confirm from attrs as it's not needed for user creation
        attrs.pop('password_confirm')
        
        return attrs
    
    def create(self, validated_data):
        """Create user and clinic profile."""
        # Extract clinic data
        clinic_data = {
            'clinic_name': validated_data.pop('clinic_name', ''),
            'nipt': validated_data.pop('nipt', ''),
            'address': validated_data.pop('address', ''),
            'contact_person': validated_data.pop('contact_person', ''),
            'phone_number': validated_data.pop('phone_number', ''),
        }
        
        # Create user
        user = CustomUser.objects.create_user(
            email=validated_data['email'],
            password=validated_data['password'],
            first_name=validated_data.get('first_name', ''),
            last_name=validated_data.get('last_name', ''),
            role='CLIENT',
            is_active=False  # Requires admin approval
        )
        
        # Create clinic profile
        ClinicProfile.objects.create(
            user=user,
            **clinic_data
        )
        
        return user


class UserLoginSerializer(serializers.Serializer):
    """Serializer for user login."""
    
    email = serializers.EmailField()
    password = serializers.CharField(style={'input_type': 'password'})
    
    def validate(self, attrs):
        """Validate user credentials."""
        email = attrs.get('email')
        password = attrs.get('password')
        
        if email and password:
            user = authenticate(
                request=self.context.get('request'),
                username=email,
                password=password
            )
            
            if not user:
                raise serializers.ValidationError('Invalid email or password.')
            
            if not user.is_active:
                raise serializers.ValidationError('User account is not active. Please wait for admin approval.')
            
            attrs['user'] = user
            return attrs
        else:
            raise serializers.ValidationError('Must include email and password.')


class UserProfileSerializer(serializers.ModelSerializer):
    """Serializer for user profile."""
    
    clinic_profile = serializers.SerializerMethodField()
    technician_profile = serializers.SerializerMethodField()
    role_display = serializers.CharField(source='get_role_display', read_only=True)
    
    class Meta:
        model = CustomUser
        fields = [
            'id', 'email', 'first_name', 'last_name', 'role', 'role_display',
            'is_active', 'date_joined', 'clinic_profile', 'technician_profile'
        ]
        read_only_fields = ['id', 'email', 'role', 'is_active', 'date_joined']
    
    def get_clinic_profile(self, obj):
        """Get clinic profile if user is a client."""
        if obj.role == 'CLIENT' and hasattr(obj, 'clinic_profile'):
            return {
                'clinic_name': obj.clinic_profile.clinic_name,
                'nipt': obj.clinic_profile.nipt,
                'address': obj.clinic_profile.address,
                'contact_person': obj.clinic_profile.contact_person,
                'phone_number': obj.clinic_profile.phone_number,
            }
        return None
    
    def get_technician_profile(self, obj):
        """Get technician profile if user is a technician."""
        if obj.role == 'TECHNICIAN' and hasattr(obj, 'technician_profile'):
            return {
                'specialization': obj.technician_profile.specialization,
            }
        return None


class PasswordChangeSerializer(serializers.Serializer):
    """Serializer for password change."""
    
    old_password = serializers.CharField(style={'input_type': 'password'})
    new_password = serializers.CharField(style={'input_type': 'password'}, validators=[validate_password])
    new_password_confirm = serializers.CharField(style={'input_type': 'password'})
    
    def validate_old_password(self, value):
        """Validate old password."""
        user = self.context['request'].user
        if not user.check_password(value):
            raise serializers.ValidationError('Old password is incorrect.')
        return value
    
    def validate(self, attrs):
        """Validate new password confirmation."""
        if attrs['new_password'] != attrs['new_password_confirm']:
            raise serializers.ValidationError("New passwords don't match.")
        return attrs
    
    def save(self):
        """Change user password."""
        user = self.context['request'].user
        user.set_password(self.validated_data['new_password'])
        user.save()
        return user
