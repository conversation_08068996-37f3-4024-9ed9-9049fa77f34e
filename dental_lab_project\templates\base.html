<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Dental Lab Management System{% endblock %}</title>
    <style>
        /* CSS Custom Properties (Variables) */
        :root {
            /* Colors */
            --primary-color: #667eea;
            --primary-dark: #5a67d8;
            --secondary-color: #764ba2;
            --success-color: #51cf66;
            --danger-color: #ff6b6b;
            --warning-color: #ffd43b;
            --info-color: #17a2b8;
            --light-color: #f8f9fa;
            --dark-color: #2c3e50;
            --muted-color: #6c757d;

            /* Gradients */
            --primary-gradient: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            --success-gradient: linear-gradient(135deg, #51cf66 0%, #40c057 100%);
            --danger-gradient: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
            --warning-gradient: linear-gradient(135deg, #ffd43b 0%, #fab005 100%);

            /* Spacing */
            --spacing-xs: 4px;
            --spacing-sm: 8px;
            --spacing-md: 16px;
            --spacing-lg: 24px;
            --spacing-xl: 32px;
            --spacing-xxl: 48px;

            /* Border Radius */
            --border-radius-sm: 4px;
            --border-radius-md: 8px;
            --border-radius-lg: 12px;
            --border-radius-xl: 16px;
            --border-radius-full: 50%;

            /* Shadows */
            --shadow-sm: 0 2px 4px rgba(0,0,0,0.1);
            --shadow-md: 0 4px 12px rgba(0,0,0,0.1);
            --shadow-lg: 0 8px 24px rgba(0,0,0,0.12);
            --shadow-xl: 0 12px 32px rgba(0,0,0,0.15);

            /* Typography */
            --font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            --font-size-xs: 0.75rem;
            --font-size-sm: 0.875rem;
            --font-size-md: 1rem;
            --font-size-lg: 1.125rem;
            --font-size-xl: 1.25rem;
            --font-size-2xl: 1.5rem;
            --font-size-3xl: 2rem;

            /* Transitions */
            --transition-fast: 0.15s ease;
            --transition-normal: 0.3s ease;
            --transition-slow: 0.5s ease;

            /* Z-index */
            --z-dropdown: 1000;
            --z-sticky: 1020;
            --z-fixed: 1030;
            --z-modal: 1040;
            --z-popover: 1050;
            --z-tooltip: 1060;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: var(--font-family);
            background: var(--light-color);
            min-height: 100vh;
            color: var(--dark-color);
            margin: 0;
            padding: 0;
            line-height: 1.6;
            font-size: var(--font-size-md);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: var(--spacing-lg);
            min-height: calc(100vh - 70px);
        }
        
        /* Modern Navbar Styles */
        .navbar {
            background: var(--primary-gradient);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            position: sticky;
            top: 0;
            z-index: var(--z-sticky);
            box-shadow: var(--shadow-lg);
        }

        .navbar-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 70px;
        }

        .navbar-brand {
            display: flex;
            align-items: center;
            gap: 12px;
            color: white;
            text-decoration: none;
            font-size: 1.5rem;
            font-weight: 700;
            transition: transform 0.3s ease;
        }

        .navbar-brand:hover {
            transform: scale(1.05);
            color: white;
        }

        .navbar-brand .logo {
            font-size: 2rem;
            filter: drop-shadow(0 2px 4px rgba(0,0,0,0.3));
        }

        .navbar-nav {
            display: flex;
            align-items: center;
            gap: 8px;
            list-style: none;
            margin: 0;
            padding: 0;
        }

        .nav-item {
            position: relative;
        }

        .nav-link {
            color: rgba(255, 255, 255, 0.9);
            text-decoration: none;
            padding: 10px 16px;
            border-radius: 8px;
            transition: all 0.3s ease;
            font-weight: 500;
            position: relative;
            overflow: hidden;
        }

        .nav-link::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .nav-link:hover::before {
            left: 100%;
        }

        .nav-link:hover {
            color: white;
            background: rgba(255, 255, 255, 0.15);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }

        .nav-link.active {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        /* User Menu Dropdown */
        .user-menu {
            position: relative;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, #ff6b6b, #feca57);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid rgba(255, 255, 255, 0.3);
        }

        .user-avatar:hover {
            transform: scale(1.1);
            border-color: rgba(255, 255, 255, 0.6);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }

        .user-dropdown {
            position: absolute;
            top: 100%;
            right: 0;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            min-width: 200px;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all 0.3s ease;
            margin-top: 10px;
            border: 1px solid rgba(0, 0, 0, 0.1);
        }

        .user-dropdown.show {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .user-dropdown::before {
            content: '';
            position: absolute;
            top: -8px;
            right: 20px;
            width: 16px;
            height: 16px;
            background: white;
            border: 1px solid rgba(0, 0, 0, 0.1);
            border-bottom: none;
            border-right: none;
            transform: rotate(45deg);
        }

        .user-info {
            padding: 16px;
            border-bottom: 1px solid #f0f0f0;
        }

        .user-name {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 4px;
        }

        .user-role {
            color: #7f8c8d;
            font-size: 0.9rem;
        }

        .dropdown-menu {
            list-style: none;
            margin: 0;
            padding: 8px 0;
        }

        .dropdown-item {
            padding: 12px 16px;
            color: #2c3e50;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 12px;
            transition: all 0.2s ease;
        }

        .dropdown-item:hover {
            background: #f8f9fa;
            color: #2c3e50;
        }

        .dropdown-item.danger:hover {
            background: #fee;
            color: #e74c3c;
        }

        /* Notification Bell */
        .notification-bell {
            position: relative;
            color: rgba(255, 255, 255, 0.9);
            font-size: 1.2rem;
            padding: 10px;
            border-radius: 8px;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .notification-bell:hover {
            color: white;
            background: rgba(255, 255, 255, 0.15);
            transform: scale(1.1);
        }

        .notification-badge {
            position: absolute;
            top: 6px;
            right: 6px;
            background: #e74c3c;
            color: white;
            border-radius: 50%;
            width: 18px;
            height: 18px;
            font-size: 0.7rem;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.2); }
            100% { transform: scale(1); }
        }

        /* Search Box */
        .navbar-search {
            position: relative;
            margin: 0 20px;
        }

        .search-input {
            background: rgba(255, 255, 255, 0.15);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 25px;
            padding: 10px 40px 10px 16px;
            color: white;
            width: 250px;
            transition: all 0.3s ease;
        }

        .search-input::placeholder {
            color: rgba(255, 255, 255, 0.7);
        }

        .search-input:focus {
            outline: none;
            background: rgba(255, 255, 255, 0.25);
            border-color: rgba(255, 255, 255, 0.5);
            width: 300px;
        }

        .search-btn {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: rgba(255, 255, 255, 0.7);
            cursor: pointer;
            transition: color 0.3s ease;
        }

        .search-btn:hover {
            color: white;
        }

        /* Mobile Menu Toggle */
        .mobile-toggle {
            display: none;
            background: none;
            border: none;
            color: white;
            font-size: 1.5rem;
            cursor: pointer;
            padding: 8px;
            border-radius: 4px;
            transition: background 0.3s ease;
        }

        .mobile-toggle:hover {
            background: rgba(255, 255, 255, 0.15);
        }
        
        .main-content {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            margin-top: 0;
            border: 1px solid rgba(0,0,0,0.05);
            position: relative;
            overflow: hidden;
        }

        .main-content::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2, #667eea);
            background-size: 200% 100%;
            animation: gradientShift 3s ease-in-out infinite;
        }

        @keyframes gradientShift {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }
        
        /* Enhanced Form Styles */
        .form-group {
            margin-bottom: 24px;
            position: relative;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #2c3e50;
            font-size: 0.95rem;
            transition: color 0.3s ease;
        }

        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 14px 16px;
            border: 2px solid #e1e8ed;
            border-radius: 8px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: #fafbfc;
            font-family: inherit;
        }

        .form-group input:focus,
        .form-group textarea:focus,
        .form-group select:focus {
            outline: none;
            border-color: #667eea;
            background: white;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            transform: translateY(-1px);
        }

        .form-group input:hover,
        .form-group textarea:hover,
        .form-group select:hover {
            border-color: #c1c8cd;
            background: white;
        }

        /* Floating Label Effect */
        .form-group.floating {
            position: relative;
        }

        .form-group.floating input,
        .form-group.floating textarea {
            padding-top: 20px;
            padding-bottom: 8px;
        }

        .form-group.floating label {
            position: absolute;
            top: 16px;
            left: 16px;
            transition: all 0.3s ease;
            pointer-events: none;
            color: #7f8c8d;
            font-weight: 400;
        }

        .form-group.floating input:focus + label,
        .form-group.floating input:not(:placeholder-shown) + label,
        .form-group.floating textarea:focus + label,
        .form-group.floating textarea:not(:placeholder-shown) + label {
            top: 4px;
            font-size: 0.8rem;
            color: #667eea;
            font-weight: 600;
        }
        
        /* Enhanced Button Styles */
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 14px 28px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }

        .btn:active {
            transform: translateY(0);
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }

        .btn-danger {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
            box-shadow: 0 4px 12px rgba(255, 107, 107, 0.3);
        }

        .btn-danger:hover {
            box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
        }

        .btn-success {
            background: linear-gradient(135deg, #51cf66 0%, #40c057 100%);
            box-shadow: 0 4px 12px rgba(81, 207, 102, 0.3);
        }

        .btn-success:hover {
            box-shadow: 0 6px 20px rgba(81, 207, 102, 0.4);
        }

        .btn-warning {
            background: linear-gradient(135deg, #ffd43b 0%, #fab005 100%);
            color: #333;
            box-shadow: 0 4px 12px rgba(255, 212, 59, 0.3);
        }

        .btn-warning:hover {
            box-shadow: 0 6px 20px rgba(255, 212, 59, 0.4);
        }

        .btn-outline {
            background: transparent;
            border: 2px solid #667eea;
            color: #667eea;
            box-shadow: none;
        }

        .btn-outline:hover {
            background: #667eea;
            color: white;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }

        .btn-sm {
            padding: 10px 20px;
            font-size: 14px;
        }

        .btn-lg {
            padding: 18px 36px;
            font-size: 18px;
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none !important;
            box-shadow: none !important;
        }
        
        /* Enhanced Alert Styles */
        .alert {
            padding: 16px 20px;
            margin-bottom: 24px;
            border-radius: 10px;
            border-left: 4px solid;
            position: relative;
            display: flex;
            align-items: center;
            gap: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            animation: slideInDown 0.3s ease-out;
        }

        @keyframes slideInDown {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .alert::before {
            content: '';
            font-size: 1.2rem;
            font-weight: bold;
        }

        .alert-success {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            color: #155724;
            border-left-color: #28a745;
        }

        .alert-success::before {
            content: '✓';
            color: #28a745;
        }

        .alert-error {
            background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
            color: #721c24;
            border-left-color: #dc3545;
        }

        .alert-error::before {
            content: '⚠';
            color: #dc3545;
        }

        .alert-info {
            background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
            color: #0c5460;
            border-left-color: #17a2b8;
        }

        .alert-info::before {
            content: 'ℹ';
            color: #17a2b8;
        }

        .alert-warning {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            color: #856404;
            border-left-color: #ffc107;
        }

        .alert-warning::before {
            content: '⚡';
            color: #ffc107;
        }

        .alert-dismissible {
            padding-right: 50px;
        }

        .alert-close {
            position: absolute;
            top: 50%;
            right: 16px;
            transform: translateY(-50%);
            background: none;
            border: none;
            font-size: 1.2rem;
            cursor: pointer;
            color: inherit;
            opacity: 0.7;
            transition: opacity 0.3s ease;
        }

        .alert-close:hover {
            opacity: 1;
        }
        
        .loading {
            text-align: center;
            padding: 20px;
        }
        
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 2s linear infinite;
            margin: 0 auto;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* Card Components */
        .card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            border: 1px solid rgba(0,0,0,0.05);
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 30px rgba(0,0,0,0.12);
        }

        .card-header {
            padding: 20px 24px;
            border-bottom: 1px solid #f0f0f0;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        }

        .card-title {
            margin: 0;
            color: #2c3e50;
            font-size: 1.25rem;
            font-weight: 600;
        }

        .card-body {
            padding: 24px;
        }

        .card-footer {
            padding: 16px 24px;
            background: #f8f9fa;
            border-top: 1px solid #f0f0f0;
        }

        /* User Info Card */
        .user-info {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 24px;
            border: none;
            box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
        }

        /* Grid System */
        .row {
            display: flex;
            flex-wrap: wrap;
            margin: 0 -12px;
        }

        .col {
            flex: 1;
            padding: 0 12px;
        }

        .col-1 { flex: 0 0 8.333333%; max-width: 8.333333%; }
        .col-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
        .col-3 { flex: 0 0 25%; max-width: 25%; }
        .col-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
        .col-6 { flex: 0 0 50%; max-width: 50%; }
        .col-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }
        .col-9 { flex: 0 0 75%; max-width: 75%; }
        .col-12 { flex: 0 0 100%; max-width: 100%; }

        .two-column {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 24px;
        }

        .three-column {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 24px;
        }

        .four-column {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 24px;
        }

        /* Utility Classes */
        .text-center { text-align: center; }
        .text-left { text-align: left; }
        .text-right { text-align: right; }

        .text-primary { color: #667eea; }
        .text-success { color: #51cf66; }
        .text-danger { color: #ff6b6b; }
        .text-warning { color: #ffd43b; }
        .text-info { color: #17a2b8; }
        .text-muted { color: #6c757d; }

        .bg-primary { background-color: #667eea; }
        .bg-success { background-color: #51cf66; }
        .bg-danger { background-color: #ff6b6b; }
        .bg-warning { background-color: #ffd43b; }
        .bg-info { background-color: #17a2b8; }
        .bg-light { background-color: #f8f9fa; }

        .m-0 { margin: 0; }
        .m-1 { margin: 8px; }
        .m-2 { margin: 16px; }
        .m-3 { margin: 24px; }
        .m-4 { margin: 32px; }

        .p-0 { padding: 0; }
        .p-1 { padding: 8px; }
        .p-2 { padding: 16px; }
        .p-3 { padding: 24px; }
        .p-4 { padding: 32px; }

        .mb-1 { margin-bottom: 8px; }
        .mb-2 { margin-bottom: 16px; }
        .mb-3 { margin-bottom: 24px; }
        .mb-4 { margin-bottom: 32px; }

        .mt-1 { margin-top: 8px; }
        .mt-2 { margin-top: 16px; }
        .mt-3 { margin-top: 24px; }
        .mt-4 { margin-top: 32px; }

        .d-none { display: none; }
        .d-block { display: block; }
        .d-flex { display: flex; }
        .d-grid { display: grid; }

        .justify-center { justify-content: center; }
        .justify-between { justify-content: space-between; }
        .justify-around { justify-content: space-around; }

        .align-center { align-items: center; }
        .align-start { align-items: flex-start; }
        .align-end { align-items: flex-end; }

        .gap-1 { gap: var(--spacing-sm); }
        .gap-2 { gap: var(--spacing-md); }
        .gap-3 { gap: var(--spacing-lg); }
        .gap-4 { gap: var(--spacing-xl); }

        /* Badge Component */
        .badge {
            display: inline-flex;
            align-items: center;
            padding: var(--spacing-xs) var(--spacing-sm);
            font-size: var(--font-size-xs);
            font-weight: 600;
            border-radius: var(--border-radius-full);
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .badge-primary {
            background: var(--primary-color);
            color: white;
        }

        .badge-success {
            background: var(--success-color);
            color: white;
        }

        .badge-danger {
            background: var(--danger-color);
            color: white;
        }

        .badge-warning {
            background: var(--warning-color);
            color: var(--dark-color);
        }

        .badge-info {
            background: var(--info-color);
            color: white;
        }

        .badge-light {
            background: var(--light-color);
            color: var(--dark-color);
            border: 1px solid #dee2e6;
        }

        /* Progress Bar */
        .progress {
            background: #e9ecef;
            border-radius: var(--border-radius-full);
            height: 8px;
            overflow: hidden;
            position: relative;
        }

        .progress-bar {
            background: var(--primary-gradient);
            height: 100%;
            transition: width var(--transition-normal);
            position: relative;
            overflow: hidden;
        }

        .progress-bar::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            bottom: 0;
            right: 0;
            background: linear-gradient(
                45deg,
                rgba(255, 255, 255, 0.2) 25%,
                transparent 25%,
                transparent 50%,
                rgba(255, 255, 255, 0.2) 50%,
                rgba(255, 255, 255, 0.2) 75%,
                transparent 75%,
                transparent
            );
            background-size: 1rem 1rem;
            animation: progress-bar-stripes 1s linear infinite;
        }

        @keyframes progress-bar-stripes {
            0% { background-position: 1rem 0; }
            100% { background-position: 0 0; }
        }

        .progress-sm { height: 4px; }
        .progress-lg { height: 12px; }

        /* Tooltip */
        .tooltip {
            position: relative;
            display: inline-block;
        }

        .tooltip::before {
            content: attr(data-tooltip);
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            background: var(--dark-color);
            color: white;
            padding: var(--spacing-sm) var(--spacing-md);
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-sm);
            white-space: nowrap;
            opacity: 0;
            visibility: hidden;
            transition: all var(--transition-normal);
            z-index: var(--z-tooltip);
            margin-bottom: 5px;
        }

        .tooltip::after {
            content: '';
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            border: 5px solid transparent;
            border-top-color: var(--dark-color);
            opacity: 0;
            visibility: hidden;
            transition: all var(--transition-normal);
        }

        .tooltip:hover::before,
        .tooltip:hover::after {
            opacity: 1;
            visibility: visible;
        }

        /* Spinner/Loading */
        .spinner-border {
            display: inline-block;
            width: 2rem;
            height: 2rem;
            vertical-align: text-bottom;
            border: 0.25em solid currentColor;
            border-right-color: transparent;
            border-radius: var(--border-radius-full);
            animation: spinner-border 0.75s linear infinite;
        }

        @keyframes spinner-border {
            to { transform: rotate(360deg); }
        }

        .spinner-sm {
            width: 1rem;
            height: 1rem;
            border-width: 0.125em;
        }

        .spinner-lg {
            width: 3rem;
            height: 3rem;
            border-width: 0.375em;
        }

        /* Status Indicators */
        .status-dot {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: var(--border-radius-full);
            margin-right: var(--spacing-sm);
        }

        .status-online { background: var(--success-color); }
        .status-offline { background: var(--muted-color); }
        .status-busy { background: var(--danger-color); }
        .status-away { background: var(--warning-color); }
        
        /* Mobile Responsive */
        @media (max-width: 768px) {
            .two-column {
                grid-template-columns: 1fr;
            }

            .navbar-container {
                padding: 0 15px;
                height: 60px;
            }

            .navbar-brand {
                font-size: 1.3rem;
            }

            .navbar-brand .logo {
                font-size: 1.5rem;
            }

            .navbar-nav {
                position: fixed;
                top: 60px;
                left: -100%;
                width: 100%;
                height: calc(100vh - 60px);
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                flex-direction: column;
                justify-content: flex-start;
                align-items: stretch;
                padding: 20px 0;
                transition: left 0.3s ease;
                gap: 0;
            }

            .navbar-nav.show {
                left: 0;
            }

            .nav-item {
                width: 100%;
            }

            .nav-link {
                padding: 16px 20px;
                border-radius: 0;
                border-bottom: 1px solid rgba(255, 255, 255, 0.1);
                width: 100%;
                text-align: left;
            }

            .navbar-search {
                margin: 0;
                padding: 0 20px;
                width: 100%;
            }

            .search-input {
                width: 100%;
            }

            .search-input:focus {
                width: 100%;
            }

            .mobile-toggle {
                display: block;
            }

            .user-menu {
                order: -1;
                margin-right: 10px;
            }

            .user-dropdown {
                right: 15px;
                left: auto;
                min-width: 180px;
            }

            .notification-bell {
                order: -2;
                margin-right: 5px;
            }

            .container {
                padding: 10px;
            }

            .main-content {
                padding: 20px;
                margin-top: 0;
            }
        }

        @media (max-width: 480px) {
            .navbar-container {
                height: 55px;
                padding: 0 10px;
            }

            .navbar-brand {
                font-size: 1.1rem;
            }

            .navbar-brand .logo {
                font-size: 1.3rem;
            }

            .user-avatar {
                width: 35px;
                height: 35px;
                font-size: 0.9rem;
            }

            .notification-bell {
                font-size: 1rem;
                padding: 8px;
            }

            .container {
                padding: 5px;
            }

            .main-content {
                padding: 15px;
            }
        }
    </style>
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Modern Navbar -->
    <nav class="navbar">
        <div class="navbar-container">
            <a href="/" class="navbar-brand">
                <span class="logo">🦷</span>
                <span>DLMS</span>
            </a>

            <!-- Search Box -->
            <div class="navbar-search">
                <input type="text" class="search-input" placeholder="Kërko porosi, pacientë..." id="globalSearch">
                <button class="search-btn" onclick="performGlobalSearch()">
                    <i>🔍</i>
                </button>
            </div>

            <!-- Navigation Links -->
            <ul class="navbar-nav" id="navbarNav">
                <li class="nav-item">
                    <a href="/" class="nav-link" data-page="dashboard">
                        <i>📊</i> Dashboard
                    </a>
                </li>
                <li class="nav-item">
                    <a href="/kanban/" class="nav-link" data-page="kanban">
                        <i>📋</i> Kanban
                    </a>
                </li>
                <li class="nav-item">
                    <a href="/test-api/" class="nav-link" data-page="api">
                        <i>🔧</i> API Test
                    </a>
                </li>
                <li class="nav-item">
                    <a href="/components/" class="nav-link" data-page="components">
                        <i>🎨</i> Components
                    </a>
                </li>
                {% if user.is_authenticated %}
                    <li class="nav-item">
                        <a href="/admin/" class="nav-link" data-page="admin">
                            <i>⚙️</i> Admin
                        </a>
                    </li>
                {% endif %}

                <!-- Notifications -->
                <li class="nav-item">
                    <div class="notification-bell" onclick="toggleNotifications()">
                        <i>🔔</i>
                        <span class="notification-badge" id="notificationCount">3</span>
                    </div>
                </li>

                <!-- User Menu -->
                {% if user.is_authenticated %}
                    <li class="nav-item user-menu">
                        <div class="user-avatar" onclick="toggleUserMenu()" id="userAvatar">
                            {{ user.first_name.0|default:user.email.0|upper }}
                        </div>
                        <div class="user-dropdown" id="userDropdown">
                            <div class="user-info">
                                <div class="user-name">{{ user.get_full_name|default:user.email }}</div>
                                <div class="user-role">{{ user.get_role_display }}</div>
                            </div>
                            <div class="dropdown-menu">
                                <a href="/auth/profile/" class="dropdown-item">
                                    <i>👤</i> Profili
                                </a>
                                <a href="/settings/" class="dropdown-item">
                                    <i>⚙️</i> Cilësimet
                                </a>
                                <a href="/help/" class="dropdown-item">
                                    <i>❓</i> Ndihmë
                                </a>
                                <hr style="margin: 8px 0; border: none; border-top: 1px solid #f0f0f0;">
                                <a href="#" class="dropdown-item danger" onclick="logout()">
                                    <i>🚪</i> Dil
                                </a>
                            </div>
                        </div>
                    </li>
                {% else %}
                    <li class="nav-item">
                        <a href="/auth/login/" class="nav-link">
                            <i>🔑</i> Hyr
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="/auth/register/" class="nav-link">
                            <i>📝</i> Regjistrohu
                        </a>
                    </li>
                {% endif %}
            </ul>

            <!-- Mobile Menu Toggle -->
            <button class="mobile-toggle" onclick="toggleMobileMenu()">
                <i id="mobileToggleIcon">☰</i>
            </button>
        </div>
    </nav>

    <div class="container">
        <div class="main-content">
            {% block content %}{% endblock %}
        </div>
    </div>
    
    <script>
        // Global JavaScript functions
        function showLoading(elementId) {
            const element = document.getElementById(elementId);
            if (element) {
                element.innerHTML = '<div class="loading"><div class="spinner"></div><p>Loading...</p></div>';
            }
        }

        function showError(elementId, message) {
            const element = document.getElementById(elementId);
            if (element) {
                element.innerHTML = `<div class="alert alert-error">${message}</div>`;
            }
        }

        function showSuccess(elementId, message) {
            const element = document.getElementById(elementId);
            if (element) {
                element.innerHTML = `<div class="alert alert-success">${message}</div>`;
            }
        }

        // Navbar functionality
        function toggleUserMenu() {
            const dropdown = document.getElementById('userDropdown');
            dropdown.classList.toggle('show');

            // Close when clicking outside
            document.addEventListener('click', function closeDropdown(e) {
                if (!e.target.closest('.user-menu')) {
                    dropdown.classList.remove('show');
                    document.removeEventListener('click', closeDropdown);
                }
            });
        }

        function toggleMobileMenu() {
            const nav = document.getElementById('navbarNav');
            const icon = document.getElementById('mobileToggleIcon');

            nav.classList.toggle('show');
            icon.textContent = nav.classList.contains('show') ? '✕' : '☰';
        }

        function toggleNotifications() {
            // TODO: Implement notifications dropdown
            console.log('Notifications clicked');
            // This will be implemented in the next phase
        }

        function performGlobalSearch() {
            const searchInput = document.getElementById('globalSearch');
            const query = searchInput.value.trim();

            if (query) {
                // TODO: Implement global search
                console.log('Searching for:', query);
                // This will redirect to search results page
                window.location.href = `/search/?q=${encodeURIComponent(query)}`;
            }
        }

        // Enhanced UI Utilities
        const UI = {
            // Toast notifications
            showToast: function(message, type = 'info', duration = 3000) {
                const toast = document.createElement('div');
                toast.className = `alert alert-${type} toast-notification`;
                toast.innerHTML = `
                    <span>${message}</span>
                    <button class="alert-close" onclick="this.parentElement.remove()">×</button>
                `;

                // Add toast styles
                toast.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    z-index: 9999;
                    min-width: 300px;
                    animation: slideInRight 0.3s ease-out;
                `;

                document.body.appendChild(toast);

                // Auto remove after duration
                setTimeout(() => {
                    if (toast.parentElement) {
                        toast.style.animation = 'slideOutRight 0.3s ease-in';
                        setTimeout(() => toast.remove(), 300);
                    }
                }, duration);
            },

            // Loading overlay
            showLoadingOverlay: function(target = document.body) {
                const overlay = document.createElement('div');
                overlay.className = 'loading-overlay';
                overlay.innerHTML = `
                    <div class="loading-content">
                        <div class="spinner-border text-primary"></div>
                        <p>Loading...</p>
                    </div>
                `;
                overlay.style.cssText = `
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background: rgba(255, 255, 255, 0.9);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    z-index: 9998;
                `;

                if (target === document.body) {
                    overlay.style.position = 'fixed';
                }

                target.style.position = 'relative';
                target.appendChild(overlay);
                return overlay;
            },

            hideLoadingOverlay: function(target = document.body) {
                const overlay = target.querySelector('.loading-overlay');
                if (overlay) {
                    overlay.remove();
                }
            },

            // Confirm dialog
            confirm: function(message, callback) {
                const modal = document.createElement('div');
                modal.className = 'modal-overlay';
                modal.innerHTML = `
                    <div class="modal-content">
                        <h3>Konfirmo</h3>
                        <p>${message}</p>
                        <div class="modal-actions">
                            <button class="btn btn-outline" onclick="this.closest('.modal-overlay').remove()">Anulo</button>
                            <button class="btn btn-danger" onclick="confirmAction()">Konfirmo</button>
                        </div>
                    </div>
                `;
                modal.style.cssText = `
                    position: fixed;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background: rgba(0, 0, 0, 0.5);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    z-index: 10000;
                `;

                window.confirmAction = function() {
                    callback();
                    modal.remove();
                    delete window.confirmAction;
                };

                document.body.appendChild(modal);
            }
        };

        // Enhanced search functionality
        let searchTimeout;
        function performGlobalSearch() {
            const searchInput = document.getElementById('globalSearch');
            const query = searchInput.value.trim();

            if (query.length < 2) {
                UI.showToast('Ju lutem shkruani të paktën 2 karaktere', 'warning');
                return;
            }

            // Show loading
            const overlay = UI.showLoadingOverlay();

            // Simulate API call (replace with actual search)
            setTimeout(() => {
                UI.hideLoadingOverlay();
                window.location.href = `/search/?q=${encodeURIComponent(query)}`;
            }, 500);
        }

        // Real-time search suggestions
        function setupSearchSuggestions() {
            const searchInput = document.getElementById('globalSearch');
            if (!searchInput) return;

            searchInput.addEventListener('input', function(e) {
                clearTimeout(searchTimeout);
                const query = e.target.value.trim();

                if (query.length >= 2) {
                    searchTimeout = setTimeout(() => {
                        // TODO: Implement search suggestions API call
                        console.log('Searching for suggestions:', query);
                    }, 300);
                }
            });
        }

        // Handle search on Enter key and setup
        document.addEventListener('DOMContentLoaded', function() {
            const searchInput = document.getElementById('globalSearch');
            if (searchInput) {
                searchInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        performGlobalSearch();
                    }
                });

                // Setup search suggestions
                setupSearchSuggestions();
            }

            // Set active nav link based on current page
            setActiveNavLink();

            // Load notification count
            loadNotificationCount();

            // Initialize tooltips
            initializeTooltips();

            // Add CSS animations
            addDynamicStyles();
        });

        function setActiveNavLink() {
            const currentPath = window.location.pathname;
            const navLinks = document.querySelectorAll('.nav-link[data-page]');

            navLinks.forEach(link => {
                const href = link.getAttribute('href');
                if (href === currentPath || (currentPath === '/' && href === '/')) {
                    link.classList.add('active');
                } else {
                    link.classList.remove('active');
                }
            });
        }

        async function loadNotificationCount() {
            try {
                // TODO: Implement notification count API
                // const response = await fetch('/api/notifications/count/');
                // const data = await response.json();
                // document.getElementById('notificationCount').textContent = data.count;

                // For now, hide badge if count is 0
                const badge = document.getElementById('notificationCount');
                if (badge && badge.textContent === '0') {
                    badge.style.display = 'none';
                }
            } catch (error) {
                console.error('Failed to load notification count:', error);
            }
        }

        async function logout() {
            try {
                const response = await fetch('/api/auth/logout/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': getCookie('csrftoken')
                    }
                });

                if (response.ok) {
                    window.location.href = '/auth/login/';
                } else {
                    alert('Logout failed');
                }
            } catch (error) {
                alert('Logout failed: ' + error.message);
            }
        }

        function getCookie(name) {
            let cookieValue = null;
            if (document.cookie && document.cookie !== '') {
                const cookies = document.cookie.split(';');
                for (let i = 0; i < cookies.length; i++) {
                    const cookie = cookies[i].trim();
                    if (cookie.substring(0, name.length + 1) === (name + '=')) {
                        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                        break;
                    }
                }
            }
            return cookieValue;
        }

        // Initialize tooltips
        function initializeTooltips() {
            // Add tooltip functionality to elements with data-tooltip attribute
            document.querySelectorAll('[data-tooltip]').forEach(element => {
                element.classList.add('tooltip');
            });
        }

        // Add dynamic CSS animations
        function addDynamicStyles() {
            const style = document.createElement('style');
            style.textContent = `
                @keyframes slideInRight {
                    from {
                        opacity: 0;
                        transform: translateX(100%);
                    }
                    to {
                        opacity: 1;
                        transform: translateX(0);
                    }
                }

                @keyframes slideOutRight {
                    from {
                        opacity: 1;
                        transform: translateX(0);
                    }
                    to {
                        opacity: 0;
                        transform: translateX(100%);
                    }
                }

                .modal-content {
                    background: white;
                    padding: 2rem;
                    border-radius: 12px;
                    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
                    max-width: 400px;
                    width: 90%;
                    animation: modalSlideIn 0.3s ease-out;
                }

                @keyframes modalSlideIn {
                    from {
                        opacity: 0;
                        transform: scale(0.8) translateY(-20px);
                    }
                    to {
                        opacity: 1;
                        transform: scale(1) translateY(0);
                    }
                }

                .modal-actions {
                    display: flex;
                    gap: 1rem;
                    justify-content: flex-end;
                    margin-top: 1.5rem;
                }

                .loading-content {
                    text-align: center;
                    color: var(--primary-color);
                }

                .loading-content p {
                    margin-top: 1rem;
                    font-weight: 500;
                }
            `;
            document.head.appendChild(style);
        }

        // Enhanced notification system
        async function loadNotificationCount() {
            try {
                // TODO: Replace with actual API call
                // const response = await fetch('/api/notifications/count/');
                // const data = await response.json();

                // Simulate API response
                const data = { count: 3, hasUnread: true };

                const badge = document.getElementById('notificationCount');
                if (badge) {
                    if (data.count > 0) {
                        badge.textContent = data.count > 99 ? '99+' : data.count;
                        badge.style.display = 'flex';

                        if (data.hasUnread) {
                            badge.style.animation = 'pulse 2s infinite';
                        }
                    } else {
                        badge.style.display = 'none';
                    }
                }
            } catch (error) {
                console.error('Failed to load notification count:', error);
            }
        }

        // Enhanced logout with confirmation
        async function logout() {
            UI.confirm('Jeni të sigurt që dëshironi të dilni?', async function() {
                const overlay = UI.showLoadingOverlay();

                try {
                    const response = await fetch('/api/auth/logout/', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRFToken': getCookie('csrftoken')
                        }
                    });

                    if (response.ok) {
                        UI.showToast('Ju keni dalë me sukses', 'success');
                        setTimeout(() => {
                            window.location.href = '/auth/login/';
                        }, 1000);
                    } else {
                        throw new Error('Logout failed');
                    }
                } catch (error) {
                    UI.hideLoadingOverlay();
                    UI.showToast('Gabim gjatë daljes: ' + error.message, 'error');
                }
            });
        }

        // Close mobile menu when clicking on a link
        document.addEventListener('DOMContentLoaded', function() {
            const navLinks = document.querySelectorAll('.nav-link');
            navLinks.forEach(link => {
                link.addEventListener('click', function() {
                    const nav = document.getElementById('navbarNav');
                    const icon = document.getElementById('mobileToggleIcon');

                    if (nav.classList.contains('show')) {
                        nav.classList.remove('show');
                        icon.textContent = '☰';
                    }
                });
            });

            // Close dropdowns when clicking outside
            document.addEventListener('click', function(e) {
                if (!e.target.closest('.user-menu')) {
                    const dropdown = document.getElementById('userDropdown');
                    if (dropdown) {
                        dropdown.classList.remove('show');
                    }
                }
            });

            // Keyboard navigation
            document.addEventListener('keydown', function(e) {
                // ESC key closes modals and dropdowns
                if (e.key === 'Escape') {
                    document.querySelectorAll('.modal-overlay').forEach(modal => modal.remove());
                    document.querySelectorAll('.user-dropdown.show').forEach(dropdown => {
                        dropdown.classList.remove('show');
                    });
                }

                // Ctrl+K opens search
                if (e.ctrlKey && e.key === 'k') {
                    e.preventDefault();
                    const searchInput = document.getElementById('globalSearch');
                    if (searchInput) {
                        searchInput.focus();
                    }
                }
            });
        });
    </script>
    {% block extra_js %}{% endblock %}
</body>
</html>
