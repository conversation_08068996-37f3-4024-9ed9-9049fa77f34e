﻿<!DOCTYPE html>
<html lang="sq">
    <!-- Include Head Section -->
    {% include 'includes/head.html' %}
<body>
    <!-- Include Navbar Component -->
    {% include 'includes/navbar.html' %}
    
    <div class="container">
        <div class="main-content">
            {% block content %}{% endblock %}
        </div>
    </div>
    
    <!-- Include Footer Component -->
    {% include 'includes/footer.html' %}
    
    <!-- Include JavaScript Utilities -->
    {% include 'includes/scripts.html' %}
    
    <!-- Page-specific JavaScript -->
    {% block extra_js %}{% endblock %}
    
    <!-- Additional CSS for layout -->
    <style>
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: var(--spacing-lg);
            min-height: calc(100vh - 70px - 200px); /* Account for navbar and footer */
            display: flex;
            flex-direction: column;
        }
        
        .main-content {
            background: white;
            padding: var(--spacing-xl);
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-md);
            margin-top: 0;
            border: 1px solid rgba(0,0,0,0.05);
            position: relative;
            overflow: hidden;
            flex: 1;
        }
        
        .main-content::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--primary-gradient);
            background-size: 200% 100%;
            animation: gradientShift 3s ease-in-out infinite;
        }
        
        @keyframes gradientShift {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }
    </style>
</body>
</html>
