from django.db import models
from django.core.validators import MinValueValidator
from decimal import Decimal
from datetime import datetime, timedelta


class Invoice(models.Model):
    """
    Model for invoices sent to clinics.
    Automatically generated when cases are shipped.
    """
    
    STATUS_CHOICES = [
        ('DRAFT', 'Draft'),
        ('SENT', '<PERSON>ë<PERSON><PERSON>r'),
        ('PAID', 'Paguar'),
        ('OVERDUE', 'Me Vonesë'),
        ('CANCELLED', 'Anuluar'),
    ]
    
    invoice_number = models.CharField(
        max_length=20,
        unique=True,
        verbose_name="Numri i Faturës"
    )
    clinic = models.ForeignKey(
        'users.ClinicProfile',
        on_delete=models.CASCADE,
        related_name='invoices',
        verbose_name="Klinika"
    )
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='DRAFT',
        verbose_name="Statusi"
    )
    issue_date = models.DateField(
        auto_now_add=True,
        verbose_name="Data e Nxjerrjes"
    )
    due_date = models.DateField(
        verbose_name="Data e Scadencës"
    )
    total_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=Decimal('0.00'),
        validators=[MinValueValidator(Decimal('0.00'))],
        verbose_name="Totali"
    )
    tax_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=Decimal('0.00'),
        validators=[MinValueValidator(Decimal('0.00'))],
        verbose_name="Tatimi"
    )
    notes = models.TextField(
        blank=True,
        verbose_name="Shënime"
    )
    pdf_file = models.FileField(
        upload_to='invoices/%Y/%m/',
        blank=True,
        null=True,
        verbose_name="PDF Faturë"
    )
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name="Krijuar më"
    )
    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name="Përditësuar më"
    )
    
    class Meta:
        verbose_name = "Faturë"
        verbose_name_plural = "Faturat"
        ordering = ['-created_at']
        
    def __str__(self):
        return f"{self.invoice_number} - {self.clinic.clinic_name} ({self.total_amount} Lekë)"
    
    def save(self, *args, **kwargs):
        """Override save to generate invoice number and due date."""
        if not self.invoice_number:
            self.invoice_number = self.generate_invoice_number()
        
        if not self.due_date:
            self.due_date = self.issue_date + timedelta(days=30)
        
        super().save(*args, **kwargs)
    
    def generate_invoice_number(self):
        """Generate a unique invoice number in format INV-YY-MM-NNNN."""
        now = datetime.now()
        year = now.strftime('%y')
        month = now.strftime('%m')
        
        # Find the last invoice for this month
        prefix = f"INV-{year}-{month}-"
        last_invoice = Invoice.objects.filter(
            invoice_number__startswith=prefix
        ).order_by('-invoice_number').first()
        
        if last_invoice:
            last_number = int(last_invoice.invoice_number.split('-')[-1])
            new_number = last_number + 1
        else:
            new_number = 1
        
        return f"{prefix}{new_number:04d}"
    
    def calculate_total(self):
        """Calculate total amount from invoice items."""
        total = sum(item.amount for item in self.items.all())
        return total
    
    def is_overdue(self):
        """Check if invoice is overdue."""
        from datetime import date
        return date.today() > self.due_date and self.status not in ['PAID', 'CANCELLED']
    
    def get_subtotal(self):
        """Get subtotal without tax."""
        return self.total_amount - self.tax_amount


class InvoiceItem(models.Model):
    """
    Model for individual items in an invoice.
    Each item typically corresponds to a completed case.
    """
    
    invoice = models.ForeignKey(
        Invoice,
        on_delete=models.CASCADE,
        related_name='items',
        verbose_name="Fatura"
    )
    case = models.ForeignKey(
        'cases.Case',
        on_delete=models.CASCADE,
        related_name='invoice_items',
        verbose_name="Porosia"
    )
    description = models.CharField(
        max_length=255,
        verbose_name="Përshkrimi"
    )
    quantity = models.IntegerField(
        default=1,
        validators=[MinValueValidator(1)],
        verbose_name="Sasia"
    )
    unit_price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(Decimal('0.01'))],
        verbose_name="Çmimi për Njësi"
    )
    amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(Decimal('0.01'))],
        verbose_name="Shuma"
    )
    
    class Meta:
        verbose_name = "Artikulli i Faturës"
        verbose_name_plural = "Artikujt e Faturës"
        ordering = ['id']
        
    def __str__(self):
        return f"{self.invoice.invoice_number} - {self.description}"
    
    def save(self, *args, **kwargs):
        """Override save to calculate amount."""
        if not self.description:
            self.description = f"{self.case.product.name} - {self.case.patient_name}"
        
        if not self.unit_price:
            self.unit_price = self.case.product.base_price
        
        self.amount = self.quantity * self.unit_price
        super().save(*args, **kwargs)


class Payment(models.Model):
    """
    Model for tracking payments received for invoices.
    """
    
    PAYMENT_METHOD_CHOICES = [
        ('CASH', 'Kesh'),
        ('BANK_TRANSFER', 'Transfertë Bankare'),
        ('CARD', 'Kartë'),
        ('CHECK', 'Çek'),
        ('OTHER', 'Tjetër'),
    ]
    
    invoice = models.ForeignKey(
        Invoice,
        on_delete=models.CASCADE,
        related_name='payments',
        verbose_name="Fatura"
    )
    amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(Decimal('0.01'))],
        verbose_name="Shuma"
    )
    payment_method = models.CharField(
        max_length=20,
        choices=PAYMENT_METHOD_CHOICES,
        verbose_name="Mënyra e Pagesës"
    )
    reference_number = models.CharField(
        max_length=100,
        blank=True,
        verbose_name="Numri i Referencës"
    )
    payment_date = models.DateField(
        verbose_name="Data e Pagesës"
    )
    notes = models.TextField(
        blank=True,
        verbose_name="Shënime"
    )
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name="Krijuar më"
    )
    
    class Meta:
        verbose_name = "Pagesë"
        verbose_name_plural = "Pagesat"
        ordering = ['-payment_date']
        
    def __str__(self):
        return f"{self.invoice.invoice_number} - {self.amount} Lekë ({self.payment_date})"
