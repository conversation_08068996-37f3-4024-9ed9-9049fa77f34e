# Generated by Django 4.2.7 on 2025-07-17 11:44

from decimal import Decimal
from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="RawMaterial",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        help_text="P.sh., 'Bllok Zirkoni Ivoclar A2 98x14mm'",
                        max_length=255,
                        verbose_name="Emri i Materialit",
                    ),
                ),
                (
                    "sku",
                    models.CharField(
                        help_text="Kodi unik i produktit",
                        max_length=50,
                        unique=True,
                        verbose_name="SKU",
                    ),
                ),
                (
                    "description",
                    models.TextField(blank=True, verbose_name="Përshkrimi"),
                ),
                (
                    "management_type",
                    models.Char<PERSON>ield(
                        choices=[
                            ("UNIT", "Për Njësi/Serializuar"),
                            ("RECIPE", "Sipas Recepturës/I Ndashëm"),
                        ],
                        help_text="UNIT: Gjurmohet si njësi individuale. RECIPE: Gjurmohet si sasi totale.",
                        max_length=10,
                        verbose_name="Lloji i Menaxhimit",
                    ),
                ),
                (
                    "unit_of_measurement",
                    models.CharField(
                        default="njësi",
                        help_text="P.sh., 'njësi', 'gram', 'ml', 'metra'",
                        max_length=20,
                        verbose_name="Njësia e Matjes",
                    ),
                ),
                (
                    "unit_cost",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        help_text="Kostoja për njësi në Lekë",
                        max_digits=10,
                        null=True,
                        validators=[
                            django.core.validators.MinValueValidator(Decimal("0.00"))
                        ],
                        verbose_name="Kostoja për Njësi",
                    ),
                ),
                (
                    "total_quantity",
                    models.DecimalField(
                        decimal_places=3,
                        default=Decimal("0.000"),
                        help_text="Sasia totale në stok (për materialet RECIPE)",
                        max_digits=10,
                        validators=[
                            django.core.validators.MinValueValidator(Decimal("0.000"))
                        ],
                        verbose_name="Sasia Totale",
                    ),
                ),
                (
                    "low_stock_threshold",
                    models.DecimalField(
                        decimal_places=3,
                        default=Decimal("10.000"),
                        help_text="Alarm kur sasia arrin këtë prag",
                        max_digits=10,
                        validators=[
                            django.core.validators.MinValueValidator(Decimal("0.000"))
                        ],
                        verbose_name="Pragu i Stokut të Ulët",
                    ),
                ),
                (
                    "supplier",
                    models.CharField(
                        blank=True, max_length=255, verbose_name="Furnizuesi"
                    ),
                ),
                ("is_active", models.BooleanField(default=True, verbose_name="Aktiv")),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Krijuar më"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Përditësuar më"),
                ),
            ],
            options={
                "verbose_name": "Lëndë e Parë",
                "verbose_name_plural": "Lëndët e Para",
                "ordering": ["name"],
            },
        ),
        migrations.CreateModel(
            name="UnitStockItem",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "serial_number",
                    models.CharField(
                        help_text="Barkodi ose numri serial unik",
                        max_length=100,
                        unique=True,
                        verbose_name="Numri Serial",
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("IN_STOCK", "Në Stok"),
                            ("IN_USE", "Në Përdorim"),
                            ("CONSUMED", "Konsumuar"),
                            ("REMNANT", "Mbetje"),
                        ],
                        default="IN_STOCK",
                        max_length=20,
                        verbose_name="Statusi",
                    ),
                ),
                (
                    "purchase_date",
                    models.DateField(
                        blank=True, null=True, verbose_name="Data e Blerjes"
                    ),
                ),
                (
                    "expiry_date",
                    models.DateField(
                        blank=True, null=True, verbose_name="Data e Skadimit"
                    ),
                ),
                ("notes", models.TextField(blank=True, verbose_name="Shënime")),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Krijuar më"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Përditësuar më"),
                ),
                (
                    "material",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="unit_items",
                        to="inventory.rawmaterial",
                        verbose_name="Materiali",
                    ),
                ),
            ],
            options={
                "verbose_name": "Njësi e Stokut",
                "verbose_name_plural": "Njësitë e Stokut",
                "ordering": ["material", "serial_number"],
            },
        ),
        migrations.CreateModel(
            name="ConsumptionLog",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "quantity_consumed",
                    models.DecimalField(
                        decimal_places=3,
                        max_digits=10,
                        validators=[
                            django.core.validators.MinValueValidator(Decimal("0.001"))
                        ],
                        verbose_name="Sasia e Konsumuar",
                    ),
                ),
                ("notes", models.TextField(blank=True, verbose_name="Shënime")),
                (
                    "timestamp",
                    models.DateTimeField(
                        auto_now_add=True, verbose_name="Koha e Konsumimit"
                    ),
                ),
                (
                    "material_used",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="consumption_logs",
                        to="inventory.rawmaterial",
                        verbose_name="Materiali i Përdorur",
                    ),
                ),
                (
                    "technician",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="material_consumption",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Tekniku",
                    ),
                ),
                (
                    "unit_item_used",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="consumption_logs",
                        to="inventory.unitstockitem",
                        verbose_name="Njësia e Përdorur",
                    ),
                ),
            ],
            options={
                "verbose_name": "Regjistri i Konsumimit",
                "verbose_name_plural": "Regjistrat e Konsumimit",
                "ordering": ["-timestamp"],
            },
        ),
    ]
