from rest_framework import serializers
from .models import Case, CaseFile, CaseLog
from users.models import Custom<PERSON>ser, ClinicProfile, TechnicianProfile
from services.models import FinalProduct


class ClinicProfileSerializer(serializers.ModelSerializer):
    """Serializer for clinic profile."""
    
    class Meta:
        model = ClinicProfile
        fields = ['clinic_name', 'nipt', 'contact_person', 'phone_number']


class TechnicianProfileSerializer(serializers.ModelSerializer):
    """Serializer for technician profile."""
    
    full_name = serializers.CharField(source='user.get_full_name', read_only=True)
    email = serializers.EmailField(source='user.email', read_only=True)
    
    class Meta:
        model = TechnicianProfile
        fields = ['full_name', 'email', 'specialization']


class FinalProductSerializer(serializers.ModelSerializer):
    """Serializer for final product."""
    
    class Meta:
        model = FinalProduct
        fields = ['id', 'name', 'base_price']


class CaseFileSerializer(serializers.ModelSerializer):
    """Serializer for case files."""
    
    file_size = serializers.SerializerMethodField()
    file_extension = serializers.SerializerMethodField()
    
    class Meta:
        model = CaseFile
        fields = ['id', 'file', 'file_type', 'description', 'file_size', 'file_extension', 'uploaded_at']
    
    def get_file_size(self, obj):
        """Get human-readable file size."""
        if obj.file:
            size = obj.file.size
            if size < 1024:
                return f"{size} B"
            elif size < 1024 * 1024:
                return f"{size / 1024:.1f} KB"
            else:
                return f"{size / (1024 * 1024):.1f} MB"
        return None
    
    def get_file_extension(self, obj):
        """Get file extension."""
        return obj.get_file_extension()


class CaseLogSerializer(serializers.ModelSerializer):
    """Serializer for case logs."""
    
    user_name = serializers.CharField(source='user.get_full_name', read_only=True)
    
    class Meta:
        model = CaseLog
        fields = ['id', 'action', 'description', 'user_name', 'timestamp']


class CaseListSerializer(serializers.ModelSerializer):
    """Serializer for case list (Kanban board)."""
    
    clinic_name = serializers.CharField(source='clinic.clinic_name', read_only=True)
    product_name = serializers.CharField(source='product.name', read_only=True)
    technician_name = serializers.CharField(source='assigned_technician.user.get_full_name', read_only=True)
    days_until_due = serializers.SerializerMethodField()
    is_overdue = serializers.SerializerMethodField()
    priority_display = serializers.CharField(source='get_priority_display', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    
    class Meta:
        model = Case
        fields = [
            'id', 'case_id', 'patient_name', 'clinic_name', 'product_name',
            'status', 'status_display', 'priority', 'priority_display',
            'technician_name', 'due_date', 'days_until_due', 'is_overdue',
            'created_at', 'updated_at'
        ]
    
    def get_days_until_due(self, obj):
        """Get days until due date."""
        return obj.get_days_until_due()
    
    def get_is_overdue(self, obj):
        """Check if case is overdue."""
        return obj.is_overdue()


class CaseDetailSerializer(serializers.ModelSerializer):
    """Serializer for case details."""
    
    clinic = ClinicProfileSerializer(read_only=True)
    product = FinalProductSerializer(read_only=True)
    assigned_technician = TechnicianProfileSerializer(read_only=True)
    files = CaseFileSerializer(many=True, read_only=True)
    logs = CaseLogSerializer(many=True, read_only=True)
    
    # Additional computed fields
    days_until_due = serializers.SerializerMethodField()
    is_overdue = serializers.SerializerMethodField()
    estimated_cost = serializers.SerializerMethodField()
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    priority_display = serializers.CharField(source='get_priority_display', read_only=True)
    creation_type_display = serializers.CharField(source='get_creation_type_display', read_only=True)
    
    class Meta:
        model = Case
        fields = [
            'id', 'case_id', 'patient_name', 'clinic', 'product', 'status', 'status_display',
            'creation_type', 'creation_type_display', 'priority', 'priority_display',
            'assigned_technician', 'due_date', 'shipped_tracking_number',
            'notes', 'color_shade', 'special_instructions',
            'days_until_due', 'is_overdue', 'estimated_cost',
            'created_at', 'updated_at', 'files', 'logs'
        ]
    
    def get_days_until_due(self, obj):
        """Get days until due date."""
        return obj.get_days_until_due()
    
    def get_is_overdue(self, obj):
        """Check if case is overdue."""
        return obj.is_overdue()
    
    def get_estimated_cost(self, obj):
        """Get estimated cost."""
        return float(obj.get_estimated_cost())


class CaseStatusChangeSerializer(serializers.Serializer):
    """Serializer for status change requests."""
    
    new_status = serializers.ChoiceField(choices=Case.STATUS_CHOICES)
    notes = serializers.CharField(max_length=500, required=False, allow_blank=True)
    
    def validate_new_status(self, value):
        """Validate that status transition is allowed."""
        case = self.context['case']
        
        # Get available transitions for current status
        available_transitions = []
        for transition in case.get_available_status_transitions():
            available_transitions.append(transition.target)
        
        if value not in available_transitions:
            raise serializers.ValidationError(
                f"Cannot transition from {case.status} to {value}. "
                f"Available transitions: {', '.join(available_transitions)}"
            )
        
        return value


class CaseCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating new cases."""
    
    class Meta:
        model = Case
        fields = [
            'patient_name', 'clinic', 'product', 'creation_type', 'priority',
            'due_date', 'notes', 'color_shade', 'special_instructions'
        ]
    
    def validate_due_date(self, value):
        """Validate that due date is not in the past."""
        from datetime import date
        if value <= date.today():
            raise serializers.ValidationError("Due date must be in the future.")
        return value
    
    def create(self, validated_data):
        """Create case with automatic case_id generation."""
        case = Case.objects.create(**validated_data)
        
        # Create initial log entry
        CaseLog.objects.create(
            case=case,
            action='CREATED',
            description=f"Case created by {self.context['request'].user.get_full_name()}",
            user=self.context['request'].user
        )
        
        return case
