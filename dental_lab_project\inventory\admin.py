from django.contrib import admin
from django.utils.html import format_html
from .models import RawMaterial, UnitStockItem, ConsumptionLog


class UnitStockItemInline(admin.TabularInline):
    """Inline admin for unit stock items."""
    model = UnitStockItem
    extra = 0
    readonly_fields = ('created_at', 'updated_at')
    fields = ('serial_number', 'status', 'assigned_case', 'purchase_date', 'expiry_date', 'notes')
    
    def get_queryset(self, request):
        """Only show for unit-managed materials."""
        return super().get_queryset(request).filter(material__management_type='UNIT')


@admin.register(RawMaterial)
class RawMaterialAdmin(admin.ModelAdmin):
    """Admin for raw materials."""
    
    list_display = ('name', 'sku', 'management_type', 'get_available_quantity', 'low_stock_indicator', 'is_active')
    list_filter = ('management_type', 'is_active', 'supplier')
    search_fields = ('name', 'sku', 'description')
    readonly_fields = ('created_at', 'updated_at')
    
    fieldsets = (
        ('Informacioni Themelor', {
            'fields': ('name', 'sku', 'description', 'management_type', 'unit_of_measurement', 'is_active')
        }),
        ('Çmimet dhe Sasia', {
            'fields': ('unit_cost', 'total_quantity', 'low_stock_threshold')
        }),
        ('Furnizimi', {
            'fields': ('supplier',)
        }),
        ('Timestamping', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def get_queryset(self, request):
        """Optimize query."""
        return super().get_queryset(request).prefetch_related('unit_items')
    
    def get_inlines(self, request, obj):
        """Show unit stock items inline only for unit-managed materials."""
        if obj and obj.management_type == 'UNIT':
            return [UnitStockItemInline]
        return []
    
    def get_available_quantity(self, obj):
        """Display available quantity based on management type."""
        quantity = obj.get_available_quantity()
        if obj.management_type == 'UNIT':
            return f"{quantity} njësi"
        else:
            return f"{quantity} {obj.unit_of_measurement}"
    get_available_quantity.short_description = "Sasia e Disponueshme"
    
    def low_stock_indicator(self, obj):
        """Show low stock indicator."""
        if obj.is_low_stock():
            return format_html('<span style="color: red;">⚠️ Stok i ulët</span>')
        return format_html('<span style="color: green;">✓ Në rregull</span>')
    low_stock_indicator.short_description = "Statusi i Stokut"


@admin.register(UnitStockItem)
class UnitStockItemAdmin(admin.ModelAdmin):
    """Admin for unit stock items."""
    
    list_display = ('material', 'serial_number', 'status', 'assigned_case', 'purchase_date', 'expiry_date')
    list_filter = ('status', 'material', 'purchase_date', 'expiry_date')
    search_fields = ('serial_number', 'material__name', 'material__sku')
    readonly_fields = ('created_at', 'updated_at')
    
    fieldsets = (
        ('Informacioni Themelor', {
            'fields': ('material', 'serial_number', 'status', 'assigned_case')
        }),
        ('Datat', {
            'fields': ('purchase_date', 'expiry_date')
        }),
        ('Shënime', {
            'fields': ('notes',)
        }),
        ('Timestamping', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def get_queryset(self, request):
        """Optimize query."""
        return super().get_queryset(request).select_related('material', 'assigned_case')


@admin.register(ConsumptionLog)
class ConsumptionLogAdmin(admin.ModelAdmin):
    """Admin for consumption logs."""
    
    list_display = ('material_used', 'quantity_consumed', 'case', 'technician', 'get_cost', 'timestamp')
    list_filter = ('material_used', 'technician', 'timestamp')
    search_fields = ('material_used__name', 'case__case_id', 'technician__email')
    readonly_fields = ('timestamp',)
    
    fieldsets = (
        ('Konsumimi', {
            'fields': ('material_used', 'unit_item_used', 'quantity_consumed', 'case', 'technician')
        }),
        ('Detaje', {
            'fields': ('notes', 'timestamp')
        }),
    )
    
    def get_queryset(self, request):
        """Optimize query."""
        return super().get_queryset(request).select_related('material_used', 'case', 'technician', 'unit_item_used')
    
    def get_cost(self, obj):
        """Display cost of consumption."""
        return f"{obj.get_cost():.2f} Lekë"
    get_cost.short_description = "Kostoja"
    
    def has_add_permission(self, request):
        """Consumption logs are usually created automatically."""
        return False
    
    def has_change_permission(self, request, obj=None):
        """Consumption logs should be read-only."""
        return False
