from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.shortcuts import get_object_or_404
from django.db.models import Prefetch
from django.utils import timezone
from django_fsm import TransitionNotAllowed

from .models import Case, CaseFile, CaseLog
from .serializers import (
    CaseListSerializer, CaseDetailSerializer, CaseStatusChangeSerializer,
    CaseCreateSerializer, CaseFileSerializer, CaseLogSerializer
)
from users.permissions import IsLabStaff, IsOwnerOrLabStaff


class CaseViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing cases.
    Provides CRUD operations and custom actions for case management.
    """
    
    queryset = Case.objects.all()
    permission_classes = [IsAuthenticated]
    
    def get_serializer_class(self):
        """Return appropriate serializer class based on action."""
        if self.action == 'list':
            return CaseListSerializer
        elif self.action == 'create':
            return CaseCreateSerializer
        elif self.action == 'change_status':
            return CaseStatusChangeSerializer
        else:
            return CaseDetailSerializer
    
    def get_queryset(self):
        """
        Filter queryset based on user permissions.
        Clients can only see their own cases.
        """
        user = self.request.user
        
        if not user.is_authenticated:
            # For anonymous users, return all cases (for testing purposes)
            return Case.objects.all().select_related(
                'clinic', 'product', 'assigned_technician__user'
            ).prefetch_related(
                'files', 'logs__user'
            )
        elif hasattr(user, 'clinic_profile') and user.role == 'CLIENT':
            # Clients can only see their own cases
            return Case.objects.filter(
                clinic=user.clinic_profile
            ).select_related(
                'clinic', 'product', 'assigned_technician__user'
            ).prefetch_related(
                'files', 'logs__user'
            )
        elif user.role == 'TECHNICIAN':
            # Technicians can see all cases but get optimized query for assigned cases
            return Case.objects.all().select_related(
                'clinic', 'product', 'assigned_technician__user'
            ).prefetch_related(
                'files', 'logs__user'
            )
        else:
            # Managers and admins can see all cases
            return Case.objects.all().select_related(
                'clinic', 'product', 'assigned_technician__user'
            ).prefetch_related(
                'files', 'logs__user'
            )
    
    def perform_create(self, serializer):
        """Create case with user context."""
        serializer.save()
    
    @action(detail=True, methods=['post'], permission_classes=[IsLabStaff])
    def change_status(self, request, pk=None):
        """
        Change case status with FSM validation.
        Only lab staff can change status.
        """
        case = self.get_object()
        serializer = CaseStatusChangeSerializer(
            data=request.data,
            context={'case': case, 'request': request}
        )
        
        if serializer.is_valid():
            new_status = serializer.validated_data['new_status']
            notes = serializer.validated_data.get('notes', '')
            
            try:
                # Get the transition method name
                transition_method = self._get_transition_method(case.status, new_status)
                
                if transition_method:
                    # Call the FSM transition method
                    getattr(case, transition_method)()
                    case.save()
                    
                    # Create log entry
                    CaseLog.objects.create(
                        case=case,
                        action='STATUS_CHANGED',
                        description=f"Status changed from '{case.get_status_display()}' to '{dict(Case.STATUS_CHOICES)[new_status]}' by {request.user.get_full_name()}" + (f". Notes: {notes}" if notes else ""),
                        user=request.user
                    )
                    
                    return Response(
                        CaseDetailSerializer(case).data,
                        status=status.HTTP_200_OK
                    )
                else:
                    return Response(
                        {'error': f'No transition method found for {case.status} -> {new_status}'},
                        status=status.HTTP_400_BAD_REQUEST
                    )
                    
            except TransitionNotAllowed as e:
                return Response(
                    {'error': str(e)},
                    status=status.HTTP_400_BAD_REQUEST
                )
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    def _get_transition_method(self, current_status, new_status):
        """Get the appropriate transition method for status change."""
        transition_map = {
            ('PENDING_PICKUP', 'RECEIVED'): 'receive_from_clinic',
            ('RECEIVED', 'IN_DESIGN'): 'start_design',
            ('IN_DESIGN', 'PENDING_APPROVAL'): 'submit_for_approval',
            ('PENDING_APPROVAL', 'IN_DESIGN'): 'request_design_changes',
            ('PENDING_APPROVAL', 'IN_PRODUCTION'): 'approve_design',
            ('IN_PRODUCTION', 'QUALITY_CONTROL'): 'complete_production',
            ('QUALITY_CONTROL', 'SENT_FOR_TRYIN'): 'send_for_tryin',
            ('SENT_FOR_TRYIN', 'COMPLETED'): 'complete_case',
            ('COMPLETED', 'SHIPPED'): 'ship_case',
        }
        
        # Handle cancellation from any status
        if new_status == 'CANCELLED':
            return 'cancel_case'
        
        return transition_map.get((current_status, new_status))
    
    @action(detail=True, methods=['post'], permission_classes=[IsLabStaff])
    def assign_technician(self, request, pk=None):
        """Assign technician to case."""
        case = self.get_object()
        technician_id = request.data.get('technician_id')
        
        if not technician_id:
            return Response(
                {'error': 'technician_id is required'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            from users.models import TechnicianProfile
            technician = TechnicianProfile.objects.get(pk=technician_id)
            
            old_technician = case.assigned_technician
            case.assigned_technician = technician
            case.save()
            
            # Create log entry
            if old_technician:
                description = f"Technician changed from {old_technician.user.get_full_name()} to {technician.user.get_full_name()}"
            else:
                description = f"Assigned to technician: {technician.user.get_full_name()}"
            
            CaseLog.objects.create(
                case=case,
                action='ASSIGNED',
                description=description,
                user=request.user
            )
            
            return Response(
                CaseDetailSerializer(case).data,
                status=status.HTTP_200_OK
            )
            
        except TechnicianProfile.DoesNotExist:
            return Response(
                {'error': 'Technician not found'},
                status=status.HTTP_404_NOT_FOUND
            )
    
    @action(detail=False, methods=['get'], permission_classes=[])
    def kanban_data(self, request):
        """
        Get optimized data for Kanban board.
        Returns cases grouped by status.
        """
        cases = self.get_queryset()
        
        # Group cases by status
        status_groups = {}
        for case in cases:
            status_key = case.status
            if status_key not in status_groups:
                status_groups[status_key] = []
            status_groups[status_key].append(CaseListSerializer(case).data)
        
        # Ensure all statuses are present
        for status_choice in Case.STATUS_CHOICES:
            if status_choice[0] not in status_groups:
                status_groups[status_choice[0]] = []
        
        return Response({
            'status_groups': status_groups,
            'status_choices': Case.STATUS_CHOICES,
            'total_cases': cases.count()
        })
    
    @action(detail=False, methods=['get'], permission_classes=[])
    def dashboard_stats(self, request):
        """Get dashboard statistics."""
        queryset = self.get_queryset()
        
        # Calculate statistics
        total_cases = queryset.count()
        overdue_cases = queryset.filter(due_date__lt=timezone.now().date()).exclude(status__in=['COMPLETED', 'SHIPPED', 'CANCELLED']).count()
        pending_approval = queryset.filter(status='PENDING_APPROVAL').count()
        in_production = queryset.filter(status='IN_PRODUCTION').count()
        
        # Cases by status
        status_counts = {}
        for status_choice in Case.STATUS_CHOICES:
            status_counts[status_choice[0]] = queryset.filter(status=status_choice[0]).count()
        
        return Response({
            'total_cases': total_cases,
            'overdue_cases': overdue_cases,
            'pending_approval': pending_approval,
            'in_production': in_production,
            'status_counts': status_counts
        })


class CaseFileViewSet(viewsets.ModelViewSet):
    """ViewSet for managing case files."""
    
    queryset = CaseFile.objects.all()
    serializer_class = CaseFileSerializer
    permission_classes = [IsAuthenticated, IsOwnerOrLabStaff]
    
    def get_queryset(self):
        """Filter files based on user permissions."""
        user = self.request.user
        
        if user.is_clinic():
            # Clients can only see files for their own cases
            return CaseFile.objects.filter(
                case__clinic=user.clinic_profile
            ).select_related('case', 'uploaded_by')
        else:
            # Lab staff can see all files
            return CaseFile.objects.all().select_related('case', 'uploaded_by')
    
    def perform_create(self, serializer):
        """Set uploaded_by to current user."""
        serializer.save(uploaded_by=self.request.user)
