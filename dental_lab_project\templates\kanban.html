{% extends 'base.html' %}

{% block title %}Kanban Dashboard - Dental Lab Management System{% endblock %}

{% block extra_css %}
<style>
    .kanban-container {
        padding: 20px 0;
    }
    
    .kanban-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 30px;
        background: white;
        padding: 20px;
        border-radius: 10px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    .kanban-title {
        color: #2c3e50;
        font-size: 2rem;
        margin: 0;
    }
    
    .kanban-controls {
        display: flex;
        gap: 15px;
        align-items: center;
    }
    
    .search-box {
        padding: 10px;
        border: 1px solid #ddd;
        border-radius: 5px;
        width: 250px;
    }
    
    .filter-select {
        padding: 10px;
        border: 1px solid #ddd;
        border-radius: 5px;
        min-width: 150px;
    }
    
    .stats-bar {
        display: flex;
        gap: 20px;
        margin-bottom: 20px;
        background: white;
        padding: 15px 20px;
        border-radius: 10px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    .stat-item {
        text-align: center;
    }
    
    .stat-number {
        font-size: 1.5rem;
        font-weight: bold;
        color: #3498db;
    }
    
    .stat-label {
        font-size: 0.9rem;
        color: #7f8c8d;
    }
    
    .kanban-board {
        display: flex;
        gap: 20px;
        overflow-x: auto;
        padding-bottom: 20px;
    }
    
    .kanban-column {
        min-width: 300px;
        background: #f8f9fa;
        border-radius: 10px;
        padding: 15px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    .column-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
        padding-bottom: 10px;
        border-bottom: 2px solid #e9ecef;
    }
    
    .column-title {
        font-weight: bold;
        color: #2c3e50;
        font-size: 1.1rem;
    }
    
    .column-count {
        background: #3498db;
        color: white;
        padding: 3px 8px;
        border-radius: 10px;
        font-size: 0.8rem;
    }
    
    .case-card {
        background: white;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 10px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        cursor: move;
        transition: all 0.2s;
        border-left: 4px solid #3498db;
    }
    
    .case-card:hover {
        box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        transform: translateY(-2px);
    }
    
    .case-card.dragging {
        opacity: 0.6;
        transform: rotate(5deg);
    }
    
    .case-id {
        font-weight: bold;
        color: #2c3e50;
        font-size: 0.9rem;
        margin-bottom: 8px;
    }
    
    .patient-name {
        font-size: 1.1rem;
        color: #34495e;
        margin-bottom: 5px;
    }
    
    .clinic-name {
        font-size: 0.9rem;
        color: #7f8c8d;
        margin-bottom: 8px;
    }
    
    .product-name {
        font-size: 0.9rem;
        color: #27ae60;
        margin-bottom: 8px;
        font-weight: 500;
    }
    
    .case-meta {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 10px;
        padding-top: 10px;
        border-top: 1px solid #ecf0f1;
    }
    
    .due-date {
        font-size: 0.8rem;
        padding: 3px 6px;
        border-radius: 3px;
    }
    
    .due-date.normal {
        background: #d5f3fe;
        color: #0c5460;
    }
    
    .due-date.warning {
        background: #fff3cd;
        color: #856404;
    }
    
    .due-date.danger {
        background: #f8d7da;
        color: #721c24;
    }
    
    .priority {
        font-size: 0.8rem;
        padding: 3px 6px;
        border-radius: 3px;
        font-weight: bold;
    }
    
    .priority.LOW {
        background: #d1ecf1;
        color: #0c5460;
    }
    
    .priority.NORMAL {
        background: #d4edda;
        color: #155724;
    }
    
    .priority.HIGH {
        background: #fff3cd;
        color: #856404;
    }
    
    .priority.URGENT {
        background: #f8d7da;
        color: #721c24;
    }
    
    .technician {
        font-size: 0.8rem;
        color: #6c757d;
        font-style: italic;
    }
    
    .drop-zone {
        min-height: 200px;
        border: 2px dashed transparent;
        border-radius: 8px;
        transition: all 0.3s;
    }
    
    .drop-zone.drag-over {
        border-color: #3498db;
        background: rgba(52, 152, 219, 0.1);
    }
    
    .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.5);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
    }
    
    .loading-content {
        background: white;
        padding: 30px;
        border-radius: 10px;
        text-align: center;
    }
    
    .modal {
        display: none;
        position: fixed;
        z-index: 10000;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0,0,0,0.5);
    }
    
    .modal-content {
        background-color: white;
        margin: 5% auto;
        padding: 20px;
        border-radius: 10px;
        width: 90%;
        max-width: 600px;
        max-height: 80vh;
        overflow-y: auto;
    }
    
    .close {
        color: #aaa;
        float: right;
        font-size: 28px;
        font-weight: bold;
        cursor: pointer;
    }
    
    .close:hover {
        color: #000;
    }
    
    .refresh-btn {
        background: #27ae60;
        border: none;
        color: white;
        padding: 10px 15px;
        border-radius: 5px;
        cursor: pointer;
        transition: background 0.3s;
    }
    
    .refresh-btn:hover {
        background: #229954;
    }
</style>
{% endblock %}

{% block content %}
<div class="kanban-container">
    <!-- Header with Controls -->
    <div class="kanban-header">
        <h1 class="kanban-title">📋 Kanban Dashboard</h1>
        <div class="kanban-controls">
            <input type="text" id="searchBox" class="search-box" placeholder="Kërko sipas ID, pacient, klinikë...">
            <select id="filterTechnician" class="filter-select">
                <option value="">Të gjithë teknikët</option>
            </select>
            <select id="filterPriority" class="filter-select">
                <option value="">Të gjitha prioritetet</option>
                <option value="URGENT">Urgjente</option>
                <option value="HIGH">E Lartë</option>
                <option value="NORMAL">Normale</option>
                <option value="LOW">E Ulët</option>
            </select>
            <button class="refresh-btn" onclick="loadKanbanData()">🔄 Rifresko</button>
        </div>
    </div>
    
    <!-- Statistics Bar -->
    <div class="stats-bar" id="statsBar">
        <div class="stat-item">
            <div class="stat-number" id="totalCases">-</div>
            <div class="stat-label">Total Porosi</div>
        </div>
        <div class="stat-item">
            <div class="stat-number" id="overdueCases">-</div>
            <div class="stat-label">Me Vonesë</div>
        </div>
        <div class="stat-item">
            <div class="stat-number" id="pendingApproval">-</div>
            <div class="stat-label">Pritje Miratimi</div>
        </div>
        <div class="stat-item">
            <div class="stat-number" id="inProduction">-</div>
            <div class="stat-label">Në Prodhim</div>
        </div>
    </div>
    
    <!-- Kanban Board -->
    <div class="kanban-board" id="kanbanBoard">
        <!-- Columns will be dynamically generated -->
    </div>
</div>

<!-- Case Detail Modal -->
<div id="caseModal" class="modal">
    <div class="modal-content">
        <span class="close">&times;</span>
        <div id="caseDetails">
            <!-- Case details will be loaded here -->
        </div>
    </div>
</div>

<!-- Loading Overlay -->
<div id="loadingOverlay" class="loading-overlay" style="display: none;">
    <div class="loading-content">
        <div class="spinner"></div>
        <p>Po ngarkon të dhënat...</p>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Global variables
    let kanbanData = null;
    let allCases = [];
    let filteredCases = [];
    
    // Status configuration with colors and display names
    const statusConfig = {
        'PENDING_PICKUP': { title: 'Në Pritje Marrjes', color: '#e74c3c' },
        'RECEIVED': { title: 'Marrë në Lab', color: '#f39c12' },
        'IN_DESIGN': { title: 'Në Dizajn', color: '#3498db' },
        'PENDING_APPROVAL': { title: 'Pritje Miratimi', color: '#9b59b6' },
        'IN_PRODUCTION': { title: 'Në Prodhim', color: '#27ae60' },
        'QUALITY_CONTROL': { title: 'Kontroll Cilësie', color: '#16a085' },
        'SENT_FOR_TRYIN': { title: 'Dërguar për Provë', color: '#2980b9' },
        'COMPLETED': { title: 'Përfunduar', color: '#27ae60' },
        'SHIPPED': { title: 'Dërguar', color: '#95a5a6' },
        'CANCELLED': { title: 'Anuluar', color: '#e74c3c' }
    };
    
    // Initialize on page load
    document.addEventListener('DOMContentLoaded', function() {
        loadKanbanData();
        setupEventListeners();
    });
    
    // Setup event listeners
    function setupEventListeners() {
        // Search functionality
        document.getElementById('searchBox').addEventListener('input', applyFilters);
        document.getElementById('filterTechnician').addEventListener('change', applyFilters);
        document.getElementById('filterPriority').addEventListener('change', applyFilters);
        
        // Modal close
        document.querySelector('.close').addEventListener('click', function() {
            document.getElementById('caseModal').style.display = 'none';
        });
        
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            const modal = document.getElementById('caseModal');
            if (event.target === modal) {
                modal.style.display = 'none';
            }
        });
    }
    
    // Load kanban data from API
    async function loadKanbanData() {
        showLoading();
        
        try {
            const [kanbanResponse, statsResponse] = await Promise.all([
                fetch('/api/cases/kanban_data/'),
                fetch('/api/cases/dashboard_stats/')
            ]);
            
            if (!kanbanResponse.ok || !statsResponse.ok) {
                throw new Error('Failed to fetch data');
            }
            
            kanbanData = await kanbanResponse.json();
            const statsData = await statsResponse.json();
            
            // Flatten all cases for filtering
            allCases = [];
            Object.values(kanbanData.status_groups).forEach(cases => {
                allCases = allCases.concat(cases);
            });
            
            filteredCases = [...allCases];
            
            updateStats(statsData);
            updateTechnicianFilter();
            renderKanbanBoard();
            
        } catch (error) {
            console.error('Error loading kanban data:', error);
            document.getElementById('kanbanBoard').innerHTML = 
                '<div class="alert alert-error">Gabim në ngarkimin e të dhënave: ' + error.message + '</div>';
        } finally {
            hideLoading();
        }
    }
    
    // Update statistics
    function updateStats(stats) {
        document.getElementById('totalCases').textContent = stats.total_cases || 0;
        document.getElementById('overdueCases').textContent = stats.overdue_cases || 0;
        document.getElementById('pendingApproval').textContent = stats.pending_approval || 0;
        document.getElementById('inProduction').textContent = stats.in_production || 0;
    }
    
    // Update technician filter dropdown
    function updateTechnicianFilter() {
        const select = document.getElementById('filterTechnician');
        const technicians = new Set();
        
        allCases.forEach(case => {
            if (case.technician_name) {
                technicians.add(case.technician_name);
            }
        });
        
        // Clear existing options except the first one
        select.innerHTML = '<option value="">Të gjithë teknikët</option>';
        
        // Add technician options
        Array.from(technicians).sort().forEach(technician => {
            const option = document.createElement('option');
            option.value = technician;
            option.textContent = technician;
            select.appendChild(option);
        });
    }
    
    // Apply filters
    function applyFilters() {
        const searchTerm = document.getElementById('searchBox').value.toLowerCase();
        const selectedTechnician = document.getElementById('filterTechnician').value;
        const selectedPriority = document.getElementById('filterPriority').value;
        
        filteredCases = allCases.filter(case => {
            // Search filter
            const matchesSearch = searchTerm === '' || 
                case.case_id.toLowerCase().includes(searchTerm) ||
                case.patient_name.toLowerCase().includes(searchTerm) ||
                case.clinic_name.toLowerCase().includes(searchTerm);
            
            // Technician filter
            const matchesTechnician = selectedTechnician === '' || 
                case.technician_name === selectedTechnician;
            
            // Priority filter
            const matchesPriority = selectedPriority === '' || 
                case.priority === selectedPriority;
            
            return matchesSearch && matchesTechnician && matchesPriority;
        });
        
        renderKanbanBoard();
    }
    
    // Render kanban board
    function renderKanbanBoard() {
        const board = document.getElementById('kanbanBoard');
        board.innerHTML = '';
        
        // Group filtered cases by status
        const filteredGroups = {};
        Object.keys(statusConfig).forEach(status => {
            filteredGroups[status] = filteredCases.filter(case => case.status === status);
        });
        
        // Create columns
        Object.entries(statusConfig).forEach(([status, config]) => {
            const column = createColumn(status, config, filteredGroups[status] || []);
            board.appendChild(column);
        });
        
        // Initialize drag and drop
        initializeDragAndDrop();
    }
    
    // Create a column
    function createColumn(status, config, cases) {
        const column = document.createElement('div');
        column.className = 'kanban-column';
        column.dataset.status = status;
        
        column.innerHTML = `
            <div class="column-header">
                <div class="column-title" style="color: ${config.color}">
                    ${config.title}
                </div>
                <div class="column-count">${cases.length}</div>
            </div>
            <div class="drop-zone" data-status="${status}">
                ${cases.map(case => createCaseCard(case)).join('')}
            </div>
        `;
        
        return column;
    }
    
    // Create a case card
    function createCaseCard(caseData) {
        const daysUntilDue = caseData.days_until_due;
        const isOverdue = caseData.is_overdue;
        
        let dueDateClass = 'normal';
        if (isOverdue) {
            dueDateClass = 'danger';
        } else if (daysUntilDue <= 1) {
            dueDateClass = 'warning';
        }
        
        const priorityClass = caseData.priority || 'NORMAL';
        const technicianDisplay = caseData.technician_name || 'Pa caktuar';
        
        return `
            <div class="case-card" draggable="true" data-case-id="${caseData.id}" onclick="showCaseDetails(${caseData.id})">
                <div class="case-id">${caseData.case_id}</div>
                <div class="patient-name">${caseData.patient_name}</div>
                <div class="clinic-name">${caseData.clinic_name}</div>
                <div class="product-name">${caseData.product_name}</div>
                <div class="case-meta">
                    <div>
                        <div class="due-date ${dueDateClass}">
                            ${isOverdue ? '⚠️ Me vonesë' : (daysUntilDue === 0 ? '🔥 Sot' : `📅 ${daysUntilDue} ditë`)}
                        </div>
                        <div class="priority ${priorityClass}">${caseData.priority_display}</div>
                    </div>
                    <div class="technician">${technicianDisplay}</div>
                </div>
            </div>
        `;
    }
    
    // Initialize drag and drop functionality
    function initializeDragAndDrop() {
        const cards = document.querySelectorAll('.case-card');
        const dropZones = document.querySelectorAll('.drop-zone');
        
        // Add drag event listeners to cards
        cards.forEach(card => {
            card.addEventListener('dragstart', handleDragStart);
            card.addEventListener('dragend', handleDragEnd);
        });
        
        // Add drop event listeners to zones
        dropZones.forEach(zone => {
            zone.addEventListener('dragover', handleDragOver);
            zone.addEventListener('dragenter', handleDragEnter);
            zone.addEventListener('dragleave', handleDragLeave);
            zone.addEventListener('drop', handleDrop);
        });
    }
    
    // Drag event handlers
    let draggedElement = null;
    
    function handleDragStart(e) {
        draggedElement = this;
        this.classList.add('dragging');
        e.dataTransfer.effectAllowed = 'move';
        e.dataTransfer.setData('text/html', this.outerHTML);
    }
    
    function handleDragEnd(e) {
        this.classList.remove('dragging');
        draggedElement = null;
    }
    
    function handleDragOver(e) {
        if (e.preventDefault) {
            e.preventDefault();
        }
        e.dataTransfer.dropEffect = 'move';
        return false;
    }
    
    function handleDragEnter(e) {
        this.classList.add('drag-over');
    }
    
    function handleDragLeave(e) {
        this.classList.remove('drag-over');
    }
    
    function handleDrop(e) {
        if (e.stopPropagation) {
            e.stopPropagation();
        }
        
        this.classList.remove('drag-over');
        
        if (draggedElement !== null) {
            const caseId = draggedElement.dataset.caseId;
            const newStatus = this.dataset.status;
            const currentStatus = findCaseStatus(caseId);
            
            if (newStatus !== currentStatus) {
                changeStatus(caseId, newStatus);
            }
        }
        
        return false;
    }
    
    // Find current status of a case
    function findCaseStatus(caseId) {
        const caseData = allCases.find(c => c.id == caseId);
        return caseData ? caseData.status : null;
    }
    
    // Change case status
    async function changeStatus(caseId, newStatus) {
        showLoading();
        
        try {
            const response = await fetch(`/api/cases/${caseId}/change_status/`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': getCookie('csrftoken')
                },
                body: JSON.stringify({
                    new_status: newStatus,
                    notes: `Status changed via Kanban board to ${statusConfig[newStatus]?.title || newStatus}`
                })
            });
            
            if (response.ok) {
                const updatedCase = await response.json();
                console.log('Status changed successfully:', updatedCase);
                
                // Reload data to reflect changes
                await loadKanbanData();
                
                showSuccess('', 'Statusi u ndryshua me sukses!');
            } else {
                const errorData = await response.json();
                throw new Error(errorData.error || 'Failed to change status');
            }
            
        } catch (error) {
            console.error('Error changing status:', error);
            showError('', 'Gabim në ndryshimin e statusit: ' + error.message);
            
            // Reload to reset the UI
            await loadKanbanData();
        } finally {
            hideLoading();
        }
    }
    
    // Show case details modal
    async function showCaseDetails(caseId) {
        const modal = document.getElementById('caseModal');
        const detailsDiv = document.getElementById('caseDetails');
        
        modal.style.display = 'block';
        detailsDiv.innerHTML = '<div class="loading"><div class="spinner"></div><p>Po ngarkon detajet...</p></div>';
        
        try {
            const response = await fetch(`/api/cases/${caseId}/`);
            if (!response.ok) {
                throw new Error('Failed to fetch case details');
            }
            
            const caseData = await response.json();
            detailsDiv.innerHTML = renderCaseDetails(caseData);
            
        } catch (error) {
            console.error('Error loading case details:', error);
            detailsDiv.innerHTML = `<div class="alert alert-error">Error loading case details: ${error.message}</div>`;
        }
    }
    
    // Render case details
    function renderCaseDetails(caseData) {
        const technicianName = caseData.assigned_technician?.full_name || 'Pa caktuar';
        const filesHtml = caseData.files.map(file => `
            <div style="margin: 5px 0;">
                <a href="${file.file}" target="_blank">${file.description || 'File'}</a>
                <span style="color: #666; font-size: 0.9em;">(${file.file_size})</span>
            </div>
        `).join('');
        
        const logsHtml = caseData.logs.slice(0, 5).map(log => `
            <div style="margin: 10px 0; padding: 10px; background: #f8f9fa; border-radius: 5px;">
                <strong>${log.user_name || 'System'}</strong> - ${log.description}
                <br><small style="color: #666;">${new Date(log.timestamp).toLocaleString()}</small>
            </div>
        `).join('');
        
        return `
            <h2>${caseData.case_id} - ${caseData.patient_name}</h2>
            
            <div class="two-column" style="margin: 20px 0;">
                <div>
                    <h3>Informacioni Bazë</h3>
                    <p><strong>Klinika:</strong> ${caseData.clinic.clinic_name}</p>
                    <p><strong>Produkti:</strong> ${caseData.product.name}</p>
                    <p><strong>Statusi:</strong> ${caseData.status_display}</p>
                    <p><strong>Prioriteti:</strong> ${caseData.priority_display}</p>
                    <p><strong>Data e Dorëzimit:</strong> ${caseData.due_date}</p>
                    <p><strong>Tekniku:</strong> ${technicianName}</p>
                </div>
                
                <div>
                    <h3>Detaje Shtesë</h3>
                    <p><strong>Lloji i Krijimit:</strong> ${caseData.creation_type_display}</p>
                    <p><strong>Ngjyra/Nuanca:</strong> ${caseData.color_shade || '-'}</p>
                    <p><strong>Kosto e Vlerësuar:</strong> €${caseData.estimated_cost}</p>
                    <p><strong>Krijuar më:</strong> ${new Date(caseData.created_at).toLocaleDateString()}</p>
                </div>
            </div>
            
            ${caseData.notes ? `
                <div style="margin: 20px 0;">
                    <h3>Shënime</h3>
                    <p style="background: #f8f9fa; padding: 10px; border-radius: 5px;">${caseData.notes}</p>
                </div>
            ` : ''}
            
            ${caseData.special_instructions ? `
                <div style="margin: 20px 0;">
                    <h3>Udhëzime të Veçanta</h3>
                    <p style="background: #fff3cd; padding: 10px; border-radius: 5px;">${caseData.special_instructions}</p>
                </div>
            ` : ''}
            
            <div style="margin: 20px 0;">
                <h3>Skedarët (${caseData.files.length})</h3>
                ${filesHtml || '<p style="color: #666;">Nuk ka skedarë të ngarkuar.</p>'}
            </div>
            
            <div style="margin: 20px 0;">
                <h3>Historiku i Fundit</h3>
                ${logsHtml || '<p style="color: #666;">Nuk ka histori të disponueshme.</p>'}
            </div>
        `;
    }
    
    // Utility functions
    function showLoading() {
        document.getElementById('loadingOverlay').style.display = 'flex';
    }
    
    function hideLoading() {
        document.getElementById('loadingOverlay').style.display = 'none';
    }
    
    function showSuccess(elementId, message) {
        // Show a toast notification
        const toast = document.createElement('div');
        toast.className = 'alert alert-success';
        toast.style.cssText = 'position: fixed; top: 20px; right: 20px; z-index: 10001; max-width: 300px;';
        toast.textContent = message;
        document.body.appendChild(toast);
        
        setTimeout(() => {
            document.body.removeChild(toast);
        }, 3000);
    }
    
    function showError(elementId, message) {
        // Show a toast notification
        const toast = document.createElement('div');
        toast.className = 'alert alert-error';
        toast.style.cssText = 'position: fixed; top: 20px; right: 20px; z-index: 10001; max-width: 300px;';
        toast.textContent = message;
        document.body.appendChild(toast);
        
        setTimeout(() => {
            document.body.removeChild(toast);
        }, 5000);
    }
    
    function getCookie(name) {
        let cookieValue = null;
        if (document.cookie && document.cookie !== '') {
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.substring(0, name.length + 1) === (name + '=')) {
                    cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                    break;
                }
            }
        }
        return cookieValue;
    }
    
    // Auto-refresh every 2 minutes
    setInterval(loadKanbanData, 120000);
</script>
{% endblock %}
