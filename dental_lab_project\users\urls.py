from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON>ult<PERSON><PERSON>er
from .views import (
    UserRegistrationView, UserLoginView, UserLogoutView,
    UserProfileView, PasswordChangeView, UserManagementViewSet,
    auth_status, login_page, register_page, profile_page
)

# Create router for viewsets
router = DefaultRouter()
router.register(r'users', UserManagementViewSet)

app_name = 'users'

urlpatterns = [
    # API endpoints
    path('api/auth/register/', UserRegistrationView.as_view(), name='register'),
    path('api/auth/login/', UserLoginView.as_view(), name='login'),
    path('api/auth/logout/', UserLogoutView.as_view(), name='logout'),
    path('api/auth/profile/', UserProfileView.as_view(), name='profile'),
    path('api/auth/change-password/', PasswordChangeView.as_view(), name='change_password'),
    path('api/auth/status/', auth_status, name='auth_status'),
    
    # User management (admin only)
    path('api/', include(router.urls)),
    
    # Web pages
    path('login/', login_page, name='login_page'),
    path('register/', register_page, name='register_page'),
    path('profile/', profile_page, name='profile_page'),
]
