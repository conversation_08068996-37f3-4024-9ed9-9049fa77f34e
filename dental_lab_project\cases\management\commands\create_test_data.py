from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import date, timedelta
import random

from users.models import ClinicProfile, TechnicianProfile
from services.models import FinalProduct
from cases.models import Case, CaseLog

User = get_user_model()


class Command(BaseCommand):
    help = 'Create sample data for testing Kanban dashboard'

    def add_arguments(self, parser):
        parser.add_argument(
            '--cases',
            type=int,
            default=20,
            help='Number of cases to create (default: 20)'
        )

    def handle(self, *args, **options):
        num_cases = options['cases']
        
        self.stdout.write('Creating sample data...')
        
        # Create users and profiles
        self.create_test_users()
        
        # Create products
        self.create_test_products()
        
        # Create cases
        self.create_test_cases(num_cases)
        
        self.stdout.write(
            self.style.SUCCESS(f'Successfully created {num_cases} test cases!')
        )

    def create_test_users(self):
        """Create test users and profiles."""
        
        # Create admin if doesn't exist
        if not User.objects.filter(email='<EMAIL>').exists():
            admin = User.objects.create_user(
                username='admin',
                email='<EMAIL>',
                password='admin123',
                role='ADMIN',
                first_name='Admin',
                last_name='User'
            )
            self.stdout.write('Created admin user: <EMAIL> / admin123')
        
        # Create manager
        if not User.objects.filter(email='<EMAIL>').exists():
            manager = User.objects.create_user(
                username='manager',
                email='<EMAIL>',
                password='manager123',
                role='MANAGER',
                first_name='Lab',
                last_name='Manager'
            )
            self.stdout.write('Created manager user: <EMAIL> / manager123')
        
        # Create technicians
        technicians_data = [
            {'email': '<EMAIL>', 'name': 'Aldo Prendi', 'spec': 'CAD/CAM'},
            {'email': '<EMAIL>', 'name': 'Maria Koci', 'spec': 'Ceramist'},
            {'email': '<EMAIL>', 'name': 'Gent Hoxha', 'spec': 'Prosthetics'},
        ]
        
        for tech_data in technicians_data:
            if not User.objects.filter(email=tech_data['email']).exists():
                user = User.objects.create_user(
                    username=tech_data['email'].split('@')[0],
                    email=tech_data['email'],
                    password='tech123',
                    role='TECHNICIAN',
                    first_name=tech_data['name'].split()[0],
                    last_name=tech_data['name'].split()[1]
                )
                
                TechnicianProfile.objects.create(
                    user=user,
                    specialization=tech_data['spec']
                )
                
                self.stdout.write(f'Created technician: {tech_data["email"]} / tech123')
        
        # Create clinics
        clinics_data = [
            {'email': '<EMAIL>', 'name': 'Dental Clinic Alpha', 'nipt': '12345678901'},
            {'email': '<EMAIL>', 'name': 'Beta Dental Care', 'nipt': '12345678902'},
            {'email': '<EMAIL>', 'name': 'Gamma Dental Studio', 'nipt': '12345678903'},
            {'email': '<EMAIL>', 'name': 'Delta Oral Health', 'nipt': '12345678904'},
        ]
        
        for clinic_data in clinics_data:
            if not User.objects.filter(email=clinic_data['email']).exists():
                user = User.objects.create_user(
                    username=clinic_data['email'].split('@')[0],
                    email=clinic_data['email'],
                    password='clinic123',
                    role='CLIENT',
                    first_name='Dr.',
                    last_name=clinic_data['name'].split()[0],
                    is_active=True
                )
                
                ClinicProfile.objects.create(
                    user=user,
                    clinic_name=clinic_data['name'],
                    nipt=clinic_data['nipt'],
                    address=f'Rruga e {clinic_data["name"]}, Tiranë, Albania',
                    contact_person=f'Dr. {clinic_data["name"].split()[0]}',
                    phone_number=f'+355 69 {random.randint(100, 999)} {random.randint(100, 999)}'
                )
                
                self.stdout.write(f'Created clinic: {clinic_data["email"]} / clinic123')

    def create_test_products(self):
        """Create test products."""
        products_data = [
            {'name': 'Kurorë Zirkoni Estetike', 'price': 150.00},
            {'name': 'Kurorë Metalceramike', 'price': 120.00},
            {'name': 'Protezë e Pjesshme', 'price': 300.00},
            {'name': 'Protezë e Plotë', 'price': 450.00},
            {'name': 'Urë Dentare 3 Njësi', 'price': 400.00},
            {'name': 'Implant Kurorë', 'price': 200.00},
            {'name': 'Kapëse Ortodontike', 'price': 50.00},
        ]
        
        for product_data in products_data:
            if not FinalProduct.objects.filter(name=product_data['name']).exists():
                FinalProduct.objects.create(
                    name=product_data['name'],
                    base_price=product_data['price'],
                    description=f'Përshkrim për {product_data["name"]}'
                )
                
        self.stdout.write(f'Created {len(products_data)} products')

    def create_test_cases(self, num_cases):
        """Create test cases with various statuses."""
        
        clinics = list(ClinicProfile.objects.all())
        products = list(FinalProduct.objects.all())
        technicians = list(TechnicianProfile.objects.all())
        
        if not clinics or not products:
            self.stdout.write(
                self.style.ERROR('No clinics or products found. Run with --verbosity 2 to see creation process.')
            )
            return
        
        statuses = [
            'PENDING_PICKUP', 'RECEIVED', 'IN_DESIGN', 'PENDING_APPROVAL',
            'IN_PRODUCTION', 'QUALITY_CONTROL', 'SENT_FOR_TRYIN', 
            'COMPLETED', 'SHIPPED'
        ]
        
        priorities = ['LOW', 'NORMAL', 'HIGH', 'URGENT']
        creation_types = ['DIGITAL', 'PHYSICAL']
        
        patient_names = [
            'Ana Marku', 'Petrit Kola', 'Sonila Hoxha', 'Besnik Shehu',
            'Elona Gjika', 'Ardit Mema', 'Blerina Tafa', 'Klodian Rama',
            'Manjola Lela', 'Ermal Balla', 'Silvana Çela', 'Fadil Hyka',
            'Teuta Krasniqi', 'Dorian Curri', 'Anxhela Doda', 'Ilir Metani'
        ]
        
        colors = [
            'A1', 'A2', 'A3', 'B1', 'B2', 'C1', 'C2', 'D1', 'D2'
        ]
        
        for i in range(num_cases):
            clinic = random.choice(clinics)
            product = random.choice(products)
            technician = random.choice(technicians) if random.random() > 0.2 else None
            
            # Generate due date (some overdue, some future)
            if random.random() < 0.15:  # 15% overdue
                due_date = date.today() - timedelta(days=random.randint(1, 5))
            elif random.random() < 0.25:  # 25% due soon
                due_date = date.today() + timedelta(days=random.randint(1, 3))
            else:  # Rest in the future
                due_date = date.today() + timedelta(days=random.randint(4, 14))
            
            case = Case.objects.create(
                clinic=clinic,
                patient_name=random.choice(patient_names),
                product=product,
                status=random.choice(statuses),
                creation_type=random.choice(creation_types),
                priority=random.choice(priorities),
                assigned_technician=technician,
                due_date=due_date,
                color_shade=random.choice(colors),
                notes=f'Test case #{i+1} - Created for demonstration purposes',
                special_instructions='Handle with care' if random.random() < 0.3 else ''
            )
            
            # Create initial log entry
            CaseLog.objects.create(
                case=case,
                action='CREATED',
                description=f'Test case created by management command',
                user=User.objects.filter(role='ADMIN').first()
            )
            
        self.stdout.write(f'Created {num_cases} test cases')
