{% extends 'base.html' %}

{% block title %}Profile - Dental Lab Management System{% endblock %}

{% block content %}
<div style="max-width: 800px; margin: 0 auto;">
    <h2 style="text-align: center; margin-bottom: 30px; color: #2c3e50;">User Profile</h2>
    
    <div id="profileMessages"></div>
    
    <!-- User Info Display -->
    <div id="userInfo" class="user-info">
        <div class="loading">
            <div class="spinner"></div>
            <p>Loading profile...</p>
        </div>
    </div>
    
    <!-- Profile Edit Form -->
    <div id="editProfileSection" style="display: none;">
        <h3 style="color: #2c3e50; margin-bottom: 20px;">Edit Profile</h3>
        <form id="editProfileForm">
            <div class="two-column">
                <div class="form-group">
                    <label for="first_name">First Name</label>
                    <input type="text" id="first_name" name="first_name">
                </div>
                
                <div class="form-group">
                    <label for="last_name">Last Name</label>
                    <input type="text" id="last_name" name="last_name">
                </div>
            </div>
            
            <div style="text-align: center; margin-top: 20px;">
                <button type="submit" class="btn btn-success">Update Profile</button>
                <button type="button" class="btn" onclick="cancelEdit()">Cancel</button>
            </div>
        </form>
    </div>
    
    <!-- Password Change Section -->
    <div style="margin-top: 40px; padding: 20px; background: #f8f9fa; border-radius: 10px;">
        <h3 style="color: #2c3e50; margin-bottom: 20px;">Change Password</h3>
        <div id="passwordMessages"></div>
        
        <form id="passwordChangeForm">
            <div class="form-group">
                <label for="old_password">Current Password</label>
                <input type="password" id="old_password" name="old_password" required>
            </div>
            
            <div class="two-column">
                <div class="form-group">
                    <label for="new_password">New Password</label>
                    <input type="password" id="new_password" name="new_password" required>
                </div>
                
                <div class="form-group">
                    <label for="new_password_confirm">Confirm New Password</label>
                    <input type="password" id="new_password_confirm" name="new_password_confirm" required>
                </div>
            </div>
            
            <div style="text-align: center;">
                <button type="submit" class="btn btn-danger">Change Password</button>
            </div>
        </form>
    </div>
    
    <!-- Admin Section for User Management -->
    <div id="adminSection" style="display: none; margin-top: 40px; padding: 20px; background: #e8f4fd; border-radius: 10px;">
        <h3 style="color: #2c3e50; margin-bottom: 20px;">User Management (Admin)</h3>
        <div id="adminMessages"></div>
        
        <div style="margin-bottom: 20px;">
            <button class="btn" onclick="loadPendingApprovals()">Pending Approvals</button>
            <button class="btn" onclick="loadAllUsers()">All Users</button>
        </div>
        
        <div id="userManagementContent"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let currentUser = null;

// Load user profile on page load
window.addEventListener('load', async function() {
    await loadUserProfile();
});

async function loadUserProfile() {
    try {
        const response = await fetch('/api/auth/profile/');
        
        if (response.ok) {
            currentUser = await response.json();
            displayUserInfo(currentUser);
            
            // Show admin section if user is admin or manager
            if (currentUser.role === 'ADMIN' || currentUser.role === 'MANAGER') {
                document.getElementById('adminSection').style.display = 'block';
            }
        } else {
            showError('profileMessages', 'Failed to load profile');
        }
    } catch (error) {
        showError('profileMessages', 'Network error loading profile');
        console.error('Profile load error:', error);
    }
}

function displayUserInfo(user) {
    let clinicInfo = '';
    if (user.clinic_profile) {
        clinicInfo = `
            <div style="margin-top: 15px;">
                <h4 style="color: #2c3e50;">Clinic Information</h4>
                <p><strong>Clinic Name:</strong> ${user.clinic_profile.clinic_name}</p>
                <p><strong>NIPT:</strong> ${user.clinic_profile.nipt}</p>
                <p><strong>Contact Person:</strong> ${user.clinic_profile.contact_person}</p>
                <p><strong>Phone:</strong> ${user.clinic_profile.phone_number}</p>
                <p><strong>Address:</strong> ${user.clinic_profile.address}</p>
            </div>
        `;
    }
    
    let techInfo = '';
    if (user.technician_profile) {
        techInfo = `
            <div style="margin-top: 15px;">
                <h4 style="color: #2c3e50;">Technician Information</h4>
                <p><strong>Specialization:</strong> ${user.technician_profile.specialization}</p>
            </div>
        `;
    }
    
    document.getElementById('userInfo').innerHTML = `
        <div style="display: flex; justify-content: between; align-items: center;">
            <div>
                <h3 style="color: #2c3e50; margin-bottom: 10px;">${user.first_name} ${user.last_name}</h3>
                <p><strong>Email:</strong> ${user.email}</p>
                <p><strong>Role:</strong> ${user.role_display}</p>
                <p><strong>Status:</strong> ${user.is_active ? '<span style="color: #27ae60;">Active</span>' : '<span style="color: #e74c3c;">Inactive</span>'}</p>
                <p><strong>Member Since:</strong> ${new Date(user.date_joined).toLocaleDateString()}</p>
            </div>
            <div>
                <button class="btn" onclick="showEditForm()">Edit Profile</button>
            </div>
        </div>
        ${clinicInfo}
        ${techInfo}
    `;
}

function showEditForm() {
    // Populate form with current data
    document.getElementById('first_name').value = currentUser.first_name || '';
    document.getElementById('last_name').value = currentUser.last_name || '';
    
    document.getElementById('editProfileSection').style.display = 'block';
}

function cancelEdit() {
    document.getElementById('editProfileSection').style.display = 'none';
}

// Handle profile update
document.getElementById('editProfileForm').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const data = Object.fromEntries(formData.entries());
    
    try {
        const response = await fetch('/api/auth/profile/', {
            method: 'PATCH',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCookie('csrftoken')
            },
            body: JSON.stringify(data)
        });
        
        if (response.ok) {
            currentUser = await response.json();
            displayUserInfo(currentUser);
            cancelEdit();
            showSuccess('profileMessages', 'Profile updated successfully');
        } else {
            const errorData = await response.json();
            showError('profileMessages', 'Failed to update profile');
        }
    } catch (error) {
        showError('profileMessages', 'Network error updating profile');
    }
});

// Handle password change
document.getElementById('passwordChangeForm').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const data = Object.fromEntries(formData.entries());
    
    if (data.new_password !== data.new_password_confirm) {
        showError('passwordMessages', 'New passwords do not match');
        return;
    }
    
    try {
        const response = await fetch('/api/auth/change-password/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCookie('csrftoken')
            },
            body: JSON.stringify(data)
        });
        
        if (response.ok) {
            showSuccess('passwordMessages', 'Password changed successfully');
            this.reset();
        } else {
            const errorData = await response.json();
            let errorMessage = 'Failed to change password';
            if (errorData.old_password) {
                errorMessage = errorData.old_password[0];
            } else if (errorData.new_password) {
                errorMessage = errorData.new_password[0];
            }
            showError('passwordMessages', errorMessage);
        }
    } catch (error) {
        showError('passwordMessages', 'Network error changing password');
    }
});

// Admin functions
async function loadPendingApprovals() {
    showLoading('userManagementContent');
    
    try {
        const response = await fetch('/api/users/pending_approvals/');
        const data = await response.json();
        
        if (data.users.length === 0) {
            document.getElementById('userManagementContent').innerHTML = '<p>No pending approvals</p>';
            return;
        }
        
        let html = '<h4>Pending Approvals (' + data.count + ')</h4><div>';
        data.users.forEach(user => {
            html += `
                <div style="border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 5px;">
                    <h5>${user.first_name} ${user.last_name} (${user.email})</h5>
                    ${user.clinic_profile ? `
                        <p><strong>Clinic:</strong> ${user.clinic_profile.clinic_name}</p>
                        <p><strong>NIPT:</strong> ${user.clinic_profile.nipt}</p>
                    ` : ''}
                    <button class="btn btn-success" onclick="activateUser(${user.id})">Approve</button>
                </div>
            `;
        });
        html += '</div>';
        
        document.getElementById('userManagementContent').innerHTML = html;
    } catch (error) {
        showError('adminMessages', 'Failed to load pending approvals');
    }
}

async function activateUser(userId) {
    try {
        const response = await fetch(`/api/users/${userId}/activate/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': getCookie('csrftoken')
            }
        });
        
        if (response.ok) {
            showSuccess('adminMessages', 'User activated successfully');
            loadPendingApprovals(); // Refresh the list
        } else {
            showError('adminMessages', 'Failed to activate user');
        }
    } catch (error) {
        showError('adminMessages', 'Network error activating user');
    }
}

async function loadAllUsers() {
    showLoading('userManagementContent');
    
    try {
        const response = await fetch('/api/users/');
        const data = await response.json();
        
        let html = '<h4>All Users</h4><div style="max-height: 400px; overflow-y: auto;">';
        data.results.forEach(user => {
            html += `
                <div style="border: 1px solid #ddd; padding: 10px; margin: 5px 0; border-radius: 5px; display: flex; justify-content: space-between; align-items: center;">
                    <div>
                        <strong>${user.first_name} ${user.last_name}</strong> (${user.email})<br>
                        <small>Role: ${user.role_display} | Status: ${user.is_active ? 'Active' : 'Inactive'}</small>
                    </div>
                    <div>
                        ${user.is_active ? 
                            `<button class="btn btn-danger" style="font-size: 12px; padding: 5px 10px;" onclick="deactivateUser(${user.id})">Deactivate</button>` :
                            `<button class="btn btn-success" style="font-size: 12px; padding: 5px 10px;" onclick="activateUser(${user.id})">Activate</button>`
                        }
                    </div>
                </div>
            `;
        });
        html += '</div>';
        
        document.getElementById('userManagementContent').innerHTML = html;
    } catch (error) {
        showError('adminMessages', 'Failed to load users');
    }
}

async function deactivateUser(userId) {
    try {
        const response = await fetch(`/api/users/${userId}/deactivate/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': getCookie('csrftoken')
            }
        });
        
        if (response.ok) {
            showSuccess('adminMessages', 'User deactivated successfully');
            loadAllUsers(); // Refresh the list
        } else {
            showError('adminMessages', 'Failed to deactivate user');
        }
    } catch (error) {
        showError('adminMessages', 'Network error deactivating user');
    }
}
</script>
{% endblock %}
