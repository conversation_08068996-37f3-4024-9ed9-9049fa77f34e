from django.db import models
from django.contrib.auth import get_user_model
from django.core.validators import MinValueValidator
from django_fsm import FSMField, transition
from decimal import Decimal
import uuid
from datetime import datetime


User = get_user_model()


class Case(models.Model):
    """
    Model for dental lab cases (orders).
    This is the core model that represents a work order from a clinic.
    """
    
    # Status choices for the FSM field
    STATUS_CHOICES = [
        ('PENDING_PICKUP', 'Në Pritje të Marrje<PERSON>'),
        ('RECEIVED', 'Marrë në Laborator'),
        ('IN_DESIGN', 'Në Dizajn'),
        ('PENDING_APPROVAL', 'Në Pritje të Miratimit'),
        ('IN_PRODUCTION', 'Në Prodhim'),
        ('QUALITY_CONTROL', 'Kontroll Cilësie'),
        ('SENT_FOR_TRYIN', 'Dërguar për Provë'),
        ('COMPLETED', 'Përfunduar'),
        ('SHIPPED', 'Dërguar'),
        ('CANCELLED', 'Anuluar'),
    ]
    
    # Creation type choices
    CREATION_TYPE_CHOICES = [
        ('DIGITAL', 'Dixhital'),
        ('PHYSICAL', 'Fizik'),
    ]
    
    # Priority choices
    PRIORITY_CHOICES = [
        ('LOW', 'E Ulët'),
        ('NORMAL', 'Normale'),
        ('HIGH', 'E Lartë'),
        ('URGENT', 'Urgjente'),
    ]
    
    case_id = models.CharField(
        max_length=20,
        unique=True,
        verbose_name="ID e Porosisë",
        help_text="ID unike e lexueshme (p.sh., LAB-25-00001)"
    )
    clinic = models.ForeignKey(
        'users.ClinicProfile',
        on_delete=models.CASCADE,
        related_name='cases',
        verbose_name="Klinika"
    )
    patient_name = models.CharField(
        max_length=255,
        verbose_name="Emri i Pacientit"
    )
    product = models.ForeignKey(
        'services.FinalProduct',
        on_delete=models.CASCADE,
        related_name='cases',
        verbose_name="Produkti"
    )
    status = FSMField(
        default='PENDING_PICKUP',
        choices=STATUS_CHOICES,
        verbose_name="Statusi"
    )
    creation_type = models.CharField(
        max_length=10,
        choices=CREATION_TYPE_CHOICES,
        default='DIGITAL',
        verbose_name="Lloji i Krijimit"
    )
    priority = models.CharField(
        max_length=10,
        choices=PRIORITY_CHOICES,
        default='NORMAL',
        verbose_name="Prioriteti"
    )
    assigned_technician = models.ForeignKey(
        'users.TechnicianProfile',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='assigned_cases',
        verbose_name="Tekniku i Caktuar"
    )
    due_date = models.DateField(
        verbose_name="Data e Dorëzimit"
    )
    shipped_tracking_number = models.CharField(
        max_length=100,
        blank=True,
        verbose_name="Numri i Gjurmimit"
    )
    notes = models.TextField(
        blank=True,
        verbose_name="Shënime"
    )
    color_shade = models.CharField(
        max_length=50,
        blank=True,
        verbose_name="Ngjyra/Nuanca"
    )
    special_instructions = models.TextField(
        blank=True,
        verbose_name="Udhëzime të Veçanta"
    )
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name="Krijuar më"
    )
    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name="Përditësuar më"
    )
    
    class Meta:
        verbose_name = "Porosi"
        verbose_name_plural = "Porositë"
        ordering = ['-created_at']
        
    def __str__(self):
        return f"{self.case_id} - {self.patient_name} ({self.clinic.clinic_name})"
    
    def save(self, *args, **kwargs):
        """Override save to generate case_id if not provided."""
        if not self.case_id:
            self.case_id = self.generate_case_id()
        super().save(*args, **kwargs)
    
    def generate_case_id(self):
        """Generate a unique case ID in format LAB-YY-MM-NNNN."""
        now = datetime.now()
        year = now.strftime('%y')
        month = now.strftime('%m')
        
        # Find the last case for this month
        prefix = f"LAB-{year}-{month}-"
        last_case = Case.objects.filter(
            case_id__startswith=prefix
        ).order_by('-case_id').first()
        
        if last_case:
            last_number = int(last_case.case_id.split('-')[-1])
            new_number = last_number + 1
        else:
            new_number = 1
        
        return f"{prefix}{new_number:04d}"
    
    def get_estimated_cost(self):
        """Calculate estimated cost based on product and materials."""
        base_cost = self.product.base_price
        material_cost = self.product.calculate_material_cost()
        return base_cost + material_cost
    
    def is_overdue(self):
        """Check if the case is overdue."""
        from datetime import date
        return date.today() > self.due_date and self.status not in ['COMPLETED', 'SHIPPED', 'CANCELLED']
    
    def get_days_until_due(self):
        """Get number of days until due date."""
        from datetime import date
        delta = self.due_date - date.today()
        return delta.days
    
    def get_available_status_transitions(self):
        """Get available status transitions for current status."""
        transition_map = {
            'PENDING_PICKUP': ['RECEIVED', 'CANCELLED'],
            'RECEIVED': ['IN_DESIGN', 'CANCELLED'],
            'IN_DESIGN': ['PENDING_APPROVAL', 'CANCELLED'],
            'PENDING_APPROVAL': ['IN_DESIGN', 'IN_PRODUCTION', 'CANCELLED'],
            'IN_PRODUCTION': ['QUALITY_CONTROL', 'CANCELLED'],
            'QUALITY_CONTROL': ['SENT_FOR_TRYIN', 'IN_PRODUCTION', 'CANCELLED'],
            'SENT_FOR_TRYIN': ['COMPLETED', 'QUALITY_CONTROL', 'CANCELLED'],
            'COMPLETED': ['SHIPPED'],
            'SHIPPED': [],
            'CANCELLED': []
        }
        
        available = transition_map.get(self.status, [])
        
        # Return as objects with target attribute for compatibility
        class TransitionTarget:
            def __init__(self, target):
                self.target = target
        
        return [TransitionTarget(target) for target in available]
    
    # FSM Transitions
    @transition(field=status, source='PENDING_PICKUP', target='RECEIVED')
    def receive_from_clinic(self):
        """Transition when physical case is received from clinic."""
        pass
    
    @transition(field=status, source='RECEIVED', target='IN_DESIGN')
    def start_design(self):
        """Transition when design work begins."""
        pass
    
    @transition(field=status, source='IN_DESIGN', target='PENDING_APPROVAL')
    def submit_for_approval(self):
        """Transition when design is submitted for client approval."""
        pass
    
    @transition(field=status, source='PENDING_APPROVAL', target='IN_DESIGN')
    def request_design_changes(self):
        """Transition when client requests design changes."""
        pass
    
    @transition(field=status, source='PENDING_APPROVAL', target='IN_PRODUCTION')
    def approve_design(self):
        """Transition when client approves design."""
        pass
    
    @transition(field=status, source='IN_PRODUCTION', target='QUALITY_CONTROL')
    def complete_production(self):
        """Transition when production is complete."""
        pass
    
    @transition(field=status, source='QUALITY_CONTROL', target='SENT_FOR_TRYIN')
    def send_for_tryin(self):
        """Transition when sending for try-in."""
        pass
    
    @transition(field=status, source='SENT_FOR_TRYIN', target='COMPLETED')
    def complete_case(self):
        """Transition when case is completed."""
        pass
    
    @transition(field=status, source='COMPLETED', target='SHIPPED')
    def ship_case(self):
        """Transition when case is shipped."""
        pass
    
    @transition(field=status, source=['PENDING_PICKUP', 'RECEIVED', 'IN_DESIGN', 'PENDING_APPROVAL', 'IN_PRODUCTION'], target='CANCELLED')
    def cancel_case(self):
        """Transition to cancel case."""
        pass


class CaseFile(models.Model):
    """
    Model for files attached to cases.
    Supports various file types including STL, images, documents.
    """
    
    FILE_TYPE_CHOICES = [
        ('STL', 'STL Model'),
        ('IMAGE', 'Image'),
        ('DOCUMENT', 'Document'),
        ('OTHER', 'Other'),
    ]
    
    case = models.ForeignKey(
        Case,
        on_delete=models.CASCADE,
        related_name='files',
        verbose_name="Porosia"
    )
    file = models.FileField(
        upload_to='case_files/%Y/%m/%d/',
        verbose_name="Skedari"
    )
    file_type = models.CharField(
        max_length=10,
        choices=FILE_TYPE_CHOICES,
        default='OTHER',
        verbose_name="Lloji i Skedarit"
    )
    description = models.CharField(
        max_length=255,
        blank=True,
        verbose_name="Përshkrimi"
    )
    uploaded_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='uploaded_files',
        verbose_name="Ngarkuar nga"
    )
    uploaded_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name="Ngarkuar më"
    )
    
    class Meta:
        verbose_name = "Skedari i Porosisë"
        verbose_name_plural = "Skedarët e Porosisë"
        ordering = ['-uploaded_at']
        
    def __str__(self):
        return f"{self.case.case_id} - {self.description or self.file.name}"
    
    def get_file_extension(self):
        """Get file extension."""
        if self.file:
            return self.file.name.split('.')[-1].lower()
        return None
    
    def is_image(self):
        """Check if file is an image."""
        image_extensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'tiff']
        return self.get_file_extension() in image_extensions
    
    def is_stl(self):
        """Check if file is an STL model."""
        return self.get_file_extension() == 'stl'


class CaseLog(models.Model):
    """
    Model for tracking case history and changes.
    Provides an audit trail for all case activities.
    """
    
    ACTION_CHOICES = [
        ('CREATED', 'Krijuar'),
        ('STATUS_CHANGED', 'Statusi u Ndryshua'),
        ('ASSIGNED', 'U Caktua'),
        ('FILE_UPLOADED', 'Skedar u Ngarkua'),
        ('COMMENT_ADDED', 'Koment u Shtua'),
        ('UPDATED', 'U Përditësua'),
    ]
    
    case = models.ForeignKey(
        Case,
        on_delete=models.CASCADE,
        related_name='logs',
        verbose_name="Porosia"
    )
    action = models.CharField(
        max_length=20,
        choices=ACTION_CHOICES,
        verbose_name="Veprimi"
    )
    description = models.TextField(
        verbose_name="Përshkrimi",
        help_text="Përshkrim i detajuar i veprimit"
    )
    user = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='case_logs',
        verbose_name="Përdoruesi"
    )
    timestamp = models.DateTimeField(
        auto_now_add=True,
        verbose_name="Koha"
    )
    
    class Meta:
        verbose_name = "Regjistri i Porosisë"
        verbose_name_plural = "Regjistrat e Porosisë"
        ordering = ['-timestamp']
        
    def __str__(self):
        return f"{self.case.case_id} - {self.get_action_display()} ({self.timestamp.strftime('%Y-%m-%d %H:%M')})"
