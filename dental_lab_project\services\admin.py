from django.contrib import admin
from .models import FinalProduct, RecipeItem


class RecipeItemInline(admin.TabularInline):
    """Inline admin for recipe items."""
    model = RecipeItem
    extra = 1
    fields = ('material', 'quantity', 'notes')
    verbose_name = "Përbërës i Recepturës"
    verbose_name_plural = "Përbërësit e Recepturës"


@admin.register(FinalProduct)
class FinalProductAdmin(admin.ModelAdmin):
    """Admin for final products."""
    
    list_display = ('name', 'base_price', 'is_active', 'created_at')
    list_filter = ('is_active', 'created_at')
    search_fields = ('name', 'description')
    readonly_fields = ('created_at', 'updated_at')
    inlines = [RecipeItemInline]
    
    fieldsets = (
        ('Informacioni Themelor', {
            'fields': ('name', 'description', 'base_price', 'is_active')
        }),
        ('Timestamping', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def get_queryset(self, request):
        """Optimize query with prefetch_related."""
        return super().get_queryset(request).prefetch_related('recipe_items__material')


@admin.register(RecipeItem)
class RecipeItemAdmin(admin.ModelAdmin):
    """Admin for recipe items."""
    
    list_display = ('product', 'material', 'quantity', 'get_total_cost')
    list_filter = ('product', 'material__management_type')
    search_fields = ('product__name', 'material__name')
    
    def get_total_cost(self, obj):
        """Display total cost for this recipe item."""
        return f"{obj.get_total_cost():.2f} Lekë"
    get_total_cost.short_description = "Kostoja Totale"
    
    def get_queryset(self, request):
        """Optimize query with select_related."""
        return super().get_queryset(request).select_related('product', 'material')
