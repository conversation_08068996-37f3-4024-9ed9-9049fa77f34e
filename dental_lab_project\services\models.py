from django.db import models
from django.core.validators import MinValueValidator
from decimal import Decimal


class FinalProduct(models.Model):
    """
    Model for final products/services offered to clients.
    These are the products that appear in the catalog and are sold to clinics.
    """
    
    name = models.CharField(
        max_length=255, 
        verbose_name="Emri i Produktit",
        help_text="P.sh., 'Kurorë Zirkoni Estetike'"
    )
    description = models.TextField(
        blank=True, 
        verbose_name="Përshkrimi",
        help_text="Përshkrim i detajuar i produktit"
    )
    base_price = models.DecimalField(
        max_digits=10, 
        decimal_places=2,
        validators=[MinValueValidator(Decimal('0.01'))],
        verbose_name="Çmimi Bazë",
        help_text="Çmimi bazë i faturimit në Lekë"
    )
    is_active = models.BooleanField(
        default=True,
        verbose_name="Aktiv",
        help_text="Nëse produkti është aktiv dhe mund të porositet"
    )
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="Krijuar më")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="Përditësuar më")
    
    class Meta:
        verbose_name = "Produkt Final"
        verbose_name_plural = "Produktet Finale"
        ordering = ['name']
        
    def __str__(self):
        return f"{self.name} ({self.base_price} Lekë)"
    
    def get_recipe_items(self):
        """Get all recipe items for this product."""
        return self.recipe_items.all()
    
    def calculate_material_cost(self):
        """Calculate the total material cost based on recipe."""
        total_cost = Decimal('0.00')
        for recipe_item in self.recipe_items.all():
            material_cost = recipe_item.material.unit_cost or Decimal('0.00')
            total_cost += material_cost * recipe_item.quantity
        return total_cost


class RecipeItem(models.Model):
    """
    Model for recipe items - defines what raw materials are needed for a final product.
    This is essentially a Bill of Materials (BOM) component.
    """
    
    product = models.ForeignKey(
        FinalProduct,
        on_delete=models.CASCADE,
        related_name='recipe_items',
        verbose_name="Produkti"
    )
    material = models.ForeignKey(
        'inventory.RawMaterial',
        on_delete=models.CASCADE,
        related_name='recipe_uses',
        verbose_name="Materiali"
    )
    quantity = models.DecimalField(
        max_digits=10,
        decimal_places=3,
        validators=[MinValueValidator(Decimal('0.001'))],
        verbose_name="Sasia",
        help_text="Sasia standarde që nevojitet për këtë produkt"
    )
    notes = models.TextField(
        blank=True,
        verbose_name="Shënime",
        help_text="Shënime shtesë për përdorimin e materialit"
    )
    
    class Meta:
        verbose_name = "Përbërës i Recepturës"
        verbose_name_plural = "Përbërësit e Recepturës"
        unique_together = ['product', 'material']
        ordering = ['product', 'material']
        
    def __str__(self):
        return f"{self.product.name} - {self.material.name} ({self.quantity})"
    
    def get_total_cost(self):
        """Calculate the total cost for this recipe item."""
        material_cost = self.material.unit_cost or Decimal('0.00')
        return material_cost * self.quantity
