from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from .views import CaseViewSet, CaseFileViewSet
from .dashboard_views import dashboard_view, kanban_view, test_api_view, api_test

# Create router and register viewsets
router = DefaultRouter()
router.register(r'cases', CaseViewSet)
router.register(r'case-files', CaseFileViewSet)

app_name = 'cases'

urlpatterns = [
    path('', dashboard_view, name='dashboard'),
    path('kanban/', kanban_view, name='kanban'),
    path('test-api/', test_api_view, name='test_api_view'),
    path('api/', include(router.urls)),
    path('api/test/', api_test, name='api_test'),
]
