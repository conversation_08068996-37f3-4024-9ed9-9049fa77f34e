from django.contrib import admin
from .models import CaseMessage, Notification


@admin.register(CaseMessage)
class CaseMessageAdmin(admin.ModelAdmin):
    """Admin for case messages."""
    
    list_display = ('case', 'sender', 'message_preview', 'is_read', 'created_at')
    list_filter = ('is_read', 'created_at', 'sender__role')
    search_fields = ('case__case_id', 'sender__email', 'message')
    readonly_fields = ('created_at',)
    
    fieldsets = (
        ('Mesazhi', {
            'fields': ('case', 'sender', 'message', 'attachment')
        }),
        ('Statusi', {
            'fields': ('is_read', 'created_at')
        }),
    )
    
    def message_preview(self, obj):
        """Show message preview."""
        return obj.message[:100] + '...' if len(obj.message) > 100 else obj.message
    message_preview.short_description = "Mesazhi"
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('case', 'sender')


@admin.register(Notification)
class NotificationAdmin(admin.ModelAdmin):
    """Admin for notifications."""
    
    list_display = ('recipient', 'notification_type', 'title', 'case', 'is_read', 'is_email_sent', 'created_at')
    list_filter = ('notification_type', 'is_read', 'is_email_sent', 'created_at')
    search_fields = ('recipient__email', 'title', 'message', 'case__case_id')
    readonly_fields = ('created_at',)
    
    fieldsets = (
        ('Njoftimi', {
            'fields': ('recipient', 'notification_type', 'title', 'message', 'case')
        }),
        ('Statusi', {
            'fields': ('is_read', 'is_email_sent', 'created_at')
        }),
    )
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('recipient', 'case')
