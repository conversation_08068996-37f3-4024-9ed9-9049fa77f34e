# Generated by Django 4.2.7 on 2025-07-17 11:46

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django_fsm


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("users", "0001_initial"),
        ("services", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="Case",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "case_id",
                    models.CharField(
                        help_text="ID unike e lexueshme (p.sh., LAB-25-00001)",
                        max_length=20,
                        unique=True,
                        verbose_name="ID e Porosisë",
                    ),
                ),
                (
                    "patient_name",
                    models.CharField(max_length=255, verbose_name="Emri i Pacientit"),
                ),
                (
                    "status",
                    django_fsm.FSMField(
                        choices=[
                            ("PENDING_PICKUP", "Në Pritje të Marrjes"),
                            ("RECEIVED", "Marrë në Laborator"),
                            ("IN_DESIGN", "Në Dizajn"),
                            ("PENDING_APPROVAL", "Në Pritje të Miratimit"),
                            ("IN_PRODUCTION", "Në Prodhim"),
                            ("QUALITY_CONTROL", "Kontroll Cilësie"),
                            ("SENT_FOR_TRYIN", "Dërguar për Provë"),
                            ("COMPLETED", "Përfunduar"),
                            ("SHIPPED", "Dërguar"),
                            ("CANCELLED", "Anuluar"),
                        ],
                        default="PENDING_PICKUP",
                        max_length=50,
                        verbose_name="Statusi",
                    ),
                ),
                (
                    "creation_type",
                    models.CharField(
                        choices=[("DIGITAL", "Dixhital"), ("PHYSICAL", "Fizik")],
                        default="DIGITAL",
                        max_length=10,
                        verbose_name="Lloji i Krijimit",
                    ),
                ),
                (
                    "priority",
                    models.CharField(
                        choices=[
                            ("LOW", "E Ulët"),
                            ("NORMAL", "Normale"),
                            ("HIGH", "E Lartë"),
                            ("URGENT", "Urgjente"),
                        ],
                        default="NORMAL",
                        max_length=10,
                        verbose_name="Prioriteti",
                    ),
                ),
                ("due_date", models.DateField(verbose_name="Data e Dorëzimit")),
                (
                    "shipped_tracking_number",
                    models.CharField(
                        blank=True, max_length=100, verbose_name="Numri i Gjurmimit"
                    ),
                ),
                ("notes", models.TextField(blank=True, verbose_name="Shënime")),
                (
                    "color_shade",
                    models.CharField(
                        blank=True, max_length=50, verbose_name="Ngjyra/Nuanca"
                    ),
                ),
                (
                    "special_instructions",
                    models.TextField(blank=True, verbose_name="Udhëzime të Veçanta"),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Krijuar më"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Përditësuar më"),
                ),
                (
                    "assigned_technician",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="assigned_cases",
                        to="users.technicianprofile",
                        verbose_name="Tekniku i Caktuar",
                    ),
                ),
                (
                    "clinic",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="cases",
                        to="users.clinicprofile",
                        verbose_name="Klinika",
                    ),
                ),
                (
                    "product",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="cases",
                        to="services.finalproduct",
                        verbose_name="Produkti",
                    ),
                ),
            ],
            options={
                "verbose_name": "Porosi",
                "verbose_name_plural": "Porositë",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="CaseLog",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "action",
                    models.CharField(
                        choices=[
                            ("CREATED", "Krijuar"),
                            ("STATUS_CHANGED", "Statusi u Ndryshua"),
                            ("ASSIGNED", "U Caktua"),
                            ("FILE_UPLOADED", "Skedar u Ngarkua"),
                            ("COMMENT_ADDED", "Koment u Shtua"),
                            ("UPDATED", "U Përditësua"),
                        ],
                        max_length=20,
                        verbose_name="Veprimi",
                    ),
                ),
                (
                    "description",
                    models.TextField(
                        help_text="Përshkrim i detajuar i veprimit",
                        verbose_name="Përshkrimi",
                    ),
                ),
                (
                    "timestamp",
                    models.DateTimeField(auto_now_add=True, verbose_name="Koha"),
                ),
                (
                    "case",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="logs",
                        to="cases.case",
                        verbose_name="Porosia",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="case_logs",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Përdoruesi",
                    ),
                ),
            ],
            options={
                "verbose_name": "Regjistri i Porosisë",
                "verbose_name_plural": "Regjistrat e Porosisë",
                "ordering": ["-timestamp"],
            },
        ),
        migrations.CreateModel(
            name="CaseFile",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "file",
                    models.FileField(
                        upload_to="case_files/%Y/%m/%d/", verbose_name="Skedari"
                    ),
                ),
                (
                    "file_type",
                    models.CharField(
                        choices=[
                            ("STL", "STL Model"),
                            ("IMAGE", "Image"),
                            ("DOCUMENT", "Document"),
                            ("OTHER", "Other"),
                        ],
                        default="OTHER",
                        max_length=10,
                        verbose_name="Lloji i Skedarit",
                    ),
                ),
                (
                    "description",
                    models.CharField(
                        blank=True, max_length=255, verbose_name="Përshkrimi"
                    ),
                ),
                (
                    "uploaded_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Ngarkuar më"),
                ),
                (
                    "case",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="files",
                        to="cases.case",
                        verbose_name="Porosia",
                    ),
                ),
                (
                    "uploaded_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="uploaded_files",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Ngarkuar nga",
                    ),
                ),
            ],
            options={
                "verbose_name": "Skedari i Porosisë",
                "verbose_name_plural": "Skedarët e Porosisë",
                "ordering": ["-uploaded_at"],
            },
        ),
    ]
