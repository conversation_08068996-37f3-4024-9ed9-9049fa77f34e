{% extends 'base.html' %}

{% block title %}Login - Dental Lab Management System{% endblock %}

{% block content %}
<div style="max-width: 400px; margin: 0 auto;">
    <h2 style="text-align: center; margin-bottom: 30px; color: #2c3e50;">Login to Your Account</h2>
    
    <div id="loginMessages"></div>
    
    <form id="loginForm">
        <div class="form-group">
            <label for="email">Email Address</label>
            <input type="email" id="email" name="email" required>
        </div>
        
        <div class="form-group">
            <label for="password">Password</label>
            <input type="password" id="password" name="password" required>
        </div>
        
        <div style="text-align: center;">
            <button type="submit" class="btn" style="width: 100%;">Login</button>
        </div>
    </form>
    
    <div style="text-align: center; margin-top: 20px;">
        <p>Don't have an account? <a href="/auth/register/" style="color: #3498db;">Register here</a></p>
    </div>
    
    <div style="margin-top: 30px; padding: 20px; background: #f8f9fa; border-radius: 5px;">
        <h3 style="color: #2c3e50; margin-bottom: 15px;">Demo Accounts</h3>
        <p><strong>Admin:</strong> <EMAIL> / admin123</p>
        <p><strong>Manager:</strong> <EMAIL> / manager123</p>
        <p><strong>Technician:</strong> <EMAIL> / tech123</p>
        <p><strong>Client:</strong> <EMAIL> / clinic123</p>
        <p style="margin-top: 10px; font-size: 14px; color: #7f8c8d;">
            <em>Note: Demo accounts may not exist yet. Create them through the admin panel or registration.</em>
        </p>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.getElementById('loginForm').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    const email = document.getElementById('email').value;
    const password = document.getElementById('password').value;
    
    showLoading('loginMessages');
    
    try {
        const response = await fetch('/api/auth/login/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCookie('csrftoken')
            },
            body: JSON.stringify({
                email: email,
                password: password
            })
        });
        
        const data = await response.json();
        
        if (response.ok) {
            showSuccess('loginMessages', `Welcome back, ${data.user.first_name || data.user.email}!`);
            
            // Redirect based on user role
            setTimeout(() => {
                if (data.user.role === 'CLIENT') {
                    window.location.href = '/';  // Client dashboard
                } else {
                    window.location.href = '/';  // Lab staff dashboard
                }
            }, 1500);
        } else {
            let errorMessage = 'Login failed';
            if (data.non_field_errors) {
                errorMessage = data.non_field_errors[0];
            } else if (data.email) {
                errorMessage = 'Email: ' + data.email[0];
            } else if (data.password) {
                errorMessage = 'Password: ' + data.password[0];
            }
            showError('loginMessages', errorMessage);
        }
    } catch (error) {
        showError('loginMessages', 'Network error. Please try again.');
        console.error('Login error:', error);
    }
});

// Check if user is already logged in
window.addEventListener('load', async function() {
    try {
        const response = await fetch('/api/auth/status/');
        const data = await response.json();
        
        if (data.authenticated) {
            document.getElementById('loginMessages').innerHTML = `
                <div class="alert alert-info">
                    You are already logged in as ${data.user.email}. 
                    <a href="/" style="color: #0c5460;">Go to Dashboard</a>
                </div>
            `;
        }
    } catch (error) {
        console.error('Auth status check failed:', error);
    }
});
</script>
{% endblock %}
