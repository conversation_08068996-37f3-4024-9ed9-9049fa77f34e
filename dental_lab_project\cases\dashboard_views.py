from django.shortcuts import render
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from .models import Case


def dashboard_view(request):
    """Main dashboard view."""
    return render(request, 'dashboard.html')


def kanban_view(request):
    """Kanban dashboard view."""
    return render(request, 'kanban.html')


def test_api_view(request):
    """Test API view."""
    return render(request, 'test_api.html')


@api_view(['GET'])
@permission_classes([])  # No authentication required for test
def api_test(request):
    """Simple API test endpoint."""
    return Response({
        'message': 'API is working!',
        'user': request.user.email if request.user.is_authenticated else 'Anonymous',
        'cases_count': Case.objects.count()
    })
