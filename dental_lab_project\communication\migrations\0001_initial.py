# Generated by Django 4.2.7 on 2025-07-17 11:51

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("cases", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="Notification",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "notification_type",
                    models.CharField(
                        choices=[
                            ("STATUS_CHANGE", "<PERSON>dryshim Statusi"),
                            ("ASSIGNMENT", "Caktim"),
                            ("APPROVAL_REQUIRED", "Kërkon Miratim"),
                            ("APPROVAL_RESPONSE", "Përgjigje Miratimi"),
                            ("INVOICE_GENERATED", "Faturë e Gjeneruar"),
                            ("PAYMENT_RECEIVED", "Pagesë e Pranuar"),
                            ("LOW_STOCK", "Stok i Ulët"),
                            ("CASE_OVERDUE", "Porosi e Vonuar"),
                            ("SYSTEM", "Sistem"),
                        ],
                        max_length=20,
                        verbose_name="Lloji",
                    ),
                ),
                ("title", models.CharField(max_length=255, verbose_name="Titulli")),
                ("message", models.TextField(verbose_name="Mesazhi")),
                ("is_read", models.BooleanField(default=False, verbose_name="Lexuar")),
                (
                    "is_email_sent",
                    models.BooleanField(default=False, verbose_name="Email u Dërgua"),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Krijuar më"),
                ),
                (
                    "case",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="notifications",
                        to="cases.case",
                        verbose_name="Porosia",
                    ),
                ),
                (
                    "recipient",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="notifications",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Marrësi",
                    ),
                ),
            ],
            options={
                "verbose_name": "Njoftim",
                "verbose_name_plural": "Njoftimet",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="CaseMessage",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("message", models.TextField(verbose_name="Mesazhi")),
                (
                    "attachment",
                    models.FileField(
                        blank=True,
                        null=True,
                        upload_to="message_attachments/%Y/%m/%d/",
                        verbose_name="Bashkëngjitje",
                    ),
                ),
                ("is_read", models.BooleanField(default=False, verbose_name="Lexuar")),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Krijuar më"),
                ),
                (
                    "case",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="messages",
                        to="cases.case",
                        verbose_name="Porosia",
                    ),
                ),
                (
                    "sender",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="sent_messages",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Dërguesi",
                    ),
                ),
            ],
            options={
                "verbose_name": "Mesazhi i Porosisë",
                "verbose_name_plural": "Mesazhet e Porosisë",
                "ordering": ["created_at"],
            },
        ),
    ]
