<!-- Modern Navbar Component -->
<nav class="navbar">
    <div class="navbar-container">
        <!-- Brand/Logo -->
        <a href="/" class="navbar-brand">
            <span class="logo">🦷</span>
            <span>DLMS</span>
        </a>
        
        <!-- Search Box -->
        <div class="navbar-search">
            <input type="text" class="search-input" placeholder="Kërko porosi, pacientë..." id="globalSearch">
            <button class="search-btn" onclick="performGlobalSearch()">
                <i>🔍</i>
            </button>
        </div>
        
        <!-- Navigation Links -->
        <ul class="navbar-nav" id="navbarNav">
            <!-- Main Navigation -->
            <li class="nav-item">
                <a href="/" class="nav-link" data-page="dashboard">
                    <i>📊</i> Dashboard
                </a>
            </li>
            <li class="nav-item">
                <a href="/kanban/" class="nav-link" data-page="kanban">
                    <i>📋</i> Kanban
                </a>
            </li>
            <li class="nav-item">
                <a href="/test-api/" class="nav-link" data-page="api">
                    <i>🔧</i> API Test
                </a>
            </li>
            <li class="nav-item">
                <a href="/components/" class="nav-link" data-page="components">
                    <i>🎨</i> Components
                </a>
            </li>
            
            <!-- Admin Link (only for authenticated users) -->
            {% if user.is_authenticated %}
                <li class="nav-item">
                    <a href="/admin/" class="nav-link" data-page="admin">
                        <i>⚙️</i> Admin
                    </a>
                </li>
            {% endif %}
            
            <!-- Notifications -->
            <li class="nav-item">
                <div class="notification-bell" onclick="toggleNotifications()">
                    <i>🔔</i>
                    <span class="notification-badge" id="notificationCount">3</span>
                </div>
            </li>
            
            <!-- User Menu or Auth Links -->
            {% if user.is_authenticated %}
                <li class="nav-item user-menu">
                    <div class="user-avatar" onclick="toggleUserMenu()" id="userAvatar">
                        {{ user.first_name.0|default:user.email.0|upper }}
                    </div>
                    <div class="user-dropdown" id="userDropdown">
                        <div class="user-info">
                            <div class="user-name">{{ user.get_full_name|default:user.email }}</div>
                            <div class="user-role">{{ user.get_role_display }}</div>
                        </div>
                        <div class="dropdown-menu">
                            <a href="/auth/profile/" class="dropdown-item">
                                <i>👤</i> Profili
                            </a>
                            <a href="/settings/" class="dropdown-item">
                                <i>⚙️</i> Cilësimet
                            </a>
                            <a href="/help/" class="dropdown-item">
                                <i>❓</i> Ndihmë
                            </a>
                            <hr style="margin: 8px 0; border: none; border-top: 1px solid #f0f0f0;">
                            <a href="#" class="dropdown-item danger" onclick="logout()">
                                <i>🚪</i> Dil
                            </a>
                        </div>
                    </div>
                </li>
            {% else %}
                <!-- Authentication Links for Non-authenticated Users -->
                <li class="nav-item">
                    <a href="/auth/login/" class="nav-link">
                        <i>🔑</i> Hyr
                    </a>
                </li>
                <li class="nav-item">
                    <a href="/auth/register/" class="nav-link">
                        <i>📝</i> Regjistrohu
                    </a>
                </li>
            {% endif %}
        </ul>
        
        <!-- Mobile Menu Toggle -->
        <button class="mobile-toggle" onclick="toggleMobileMenu()">
            <i id="mobileToggleIcon">☰</i>
        </button>
    </div>
</nav>

<!-- Navbar JavaScript -->
<script>
// Navbar-specific JavaScript functions
(function() {
    'use strict';
    
    // Initialize navbar when DOM is loaded
    document.addEventListener('DOMContentLoaded', function() {
        initializeNavbar();
    });
    
    function initializeNavbar() {
        // Set active nav link based on current page
        setActiveNavLink();
        
        // Load notification count
        loadNotificationCount();
        
        // Setup mobile menu close on link click
        setupMobileMenuClose();
        
        // Setup search functionality
        setupNavbarSearch();
        
        // Setup dropdown close on outside click
        setupDropdownClose();
    }
    
    function setActiveNavLink() {
        const currentPath = window.location.pathname;
        const navLinks = document.querySelectorAll('.nav-link[data-page]');
        
        navLinks.forEach(link => {
            const href = link.getAttribute('href');
            if (href === currentPath || (currentPath === '/' && href === '/')) {
                link.classList.add('active');
            } else {
                link.classList.remove('active');
            }
        });
    }
    
    function setupMobileMenuClose() {
        const navLinks = document.querySelectorAll('.nav-link');
        navLinks.forEach(link => {
            link.addEventListener('click', function() {
                const nav = document.getElementById('navbarNav');
                const icon = document.getElementById('mobileToggleIcon');
                
                if (nav && nav.classList.contains('show')) {
                    nav.classList.remove('show');
                    if (icon) icon.textContent = '☰';
                }
            });
        });
    }
    
    function setupNavbarSearch() {
        const searchInput = document.getElementById('globalSearch');
        if (searchInput) {
            // Enter key search
            searchInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    performGlobalSearch();
                }
            });
            
            // Setup search suggestions (if needed)
            window.setupSearchSuggestions();
        }
    }
    
    function setupDropdownClose() {
        document.addEventListener('click', function(e) {
            if (!e.target.closest('.user-menu')) {
                const dropdown = document.getElementById('userDropdown');
                if (dropdown) {
                    dropdown.classList.remove('show');
                }
            }
        });
    }
    
    async function loadNotificationCount() {
        try {
            // TODO: Replace with actual API call
            // const response = await fetch('/api/notifications/count/');
            // const data = await response.json();
            
            // Simulate API response
            const data = { count: 3, hasUnread: true };
            
            const badge = document.getElementById('notificationCount');
            if (badge) {
                if (data.count > 0) {
                    badge.textContent = data.count > 99 ? '99+' : data.count;
                    badge.style.display = 'flex';
                    
                    if (data.hasUnread) {
                        badge.style.animation = 'pulse 2s infinite';
                    }
                } else {
                    badge.style.display = 'none';
                }
            }
        } catch (error) {
            console.error('Failed to load notification count:', error);
        }
    }
    
})();

// Global navbar functions (accessible from outside)
window.toggleUserMenu = function() {
    const dropdown = document.getElementById('userDropdown');
    if (dropdown) {
        dropdown.classList.toggle('show');
    }
};

window.toggleMobileMenu = function() {
    const nav = document.getElementById('navbarNav');
    const icon = document.getElementById('mobileToggleIcon');
    
    if (nav) {
        nav.classList.toggle('show');
        if (icon) {
            icon.textContent = nav.classList.contains('show') ? '✕' : '☰';
        }
    }
};

window.toggleNotifications = function() {
    // TODO: Implement notifications dropdown
    console.log('Notifications clicked');
    // This will be implemented in the next phase
    if (window.UI) {
        window.UI.showToast('Notifications feature coming soon!', 'info');
    }
};

window.performGlobalSearch = function() {
    const searchInput = document.getElementById('globalSearch');
    const query = searchInput ? searchInput.value.trim() : '';

    if (query.length < 2) {
        if (window.UI) {
            window.UI.showToast('Ju lutem shkruani të paktën 2 karaktere', 'warning');
        }
        return;
    }

    // Show loading if UI utility is available
    if (window.UI) {
        const overlay = window.UI.showLoadingOverlay();

        // Simulate API call (replace with actual search)
        setTimeout(() => {
            window.UI.hideLoadingOverlay();
            window.location.href = `/search/?q=${encodeURIComponent(query)}`;
        }, 500);
    } else {
        // Fallback without UI utilities
        window.location.href = `/search/?q=${encodeURIComponent(query)}`;
    }
};

window.setupSearchSuggestions = function() {
    const searchInput = document.getElementById('globalSearch');
    if (!searchInput) return;

    let searchTimeout;
    searchInput.addEventListener('input', function(e) {
        clearTimeout(searchTimeout);
        const query = e.target.value.trim();

        if (query.length >= 2) {
            searchTimeout = setTimeout(() => {
                // TODO: Implement search suggestions API call
                console.log('Searching for suggestions:', query);
            }, 300);
        }
    });
};
</script>
