{% extends 'base.html' %}

{% block title %}Components Demo - Dental Lab Management System{% endblock %}

{% block content %}
<div class="mb-4">
    <h1 class="text-center mb-3">🎨 UI Components Demo</h1>
    <p class="text-center text-muted">Demonstrim i komponenteve të reja të UI</p>
</div>

<!-- Cards Demo -->
<div class="row mb-4">
    <div class="col-4">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Card me Header</h3>
            </div>
            <div class="card-body">
                <p>Kjo është një card me header dhe body. Përdoret për të organizuar përmbajtjen.</p>
                <button class="btn btn-primary">Action Button</button>
            </div>
        </div>
    </div>
    <div class="col-4">
        <div class="card">
            <div class="card-body">
                <h3 class="card-title">Card pa Header</h3>
                <p>Card i thjeshtë pa header, vetëm me body content.</p>
                <div class="d-flex gap-2">
                    <button class="btn btn-success btn-sm">Success</button>
                    <button class="btn btn-danger btn-sm">Danger</button>
                </div>
            </div>
        </div>
    </div>
    <div class="col-4">
        <div class="card">
            <div class="card-body">
                <h3 class="card-title">Card me Footer</h3>
                <p>Ky card ka footer për informacione shtesë.</p>
            </div>
            <div class="card-footer">
                <small class="text-muted">Footer information</small>
            </div>
        </div>
    </div>
</div>

<!-- Buttons Demo -->
<div class="card mb-4">
    <div class="card-header">
        <h3 class="card-title">Buttons</h3>
    </div>
    <div class="card-body">
        <div class="mb-3">
            <h5>Button Variants</h5>
            <div class="d-flex gap-2 mb-3">
                <button class="btn btn-primary">Primary</button>
                <button class="btn btn-success">Success</button>
                <button class="btn btn-danger">Danger</button>
                <button class="btn btn-warning">Warning</button>
                <button class="btn btn-outline">Outline</button>
            </div>
        </div>
        
        <div class="mb-3">
            <h5>Button Sizes</h5>
            <div class="d-flex align-center gap-2">
                <button class="btn btn-primary btn-sm">Small</button>
                <button class="btn btn-primary">Normal</button>
                <button class="btn btn-primary btn-lg">Large</button>
            </div>
        </div>
        
        <div>
            <h5>Button States</h5>
            <div class="d-flex gap-2">
                <button class="btn btn-primary">Normal</button>
                <button class="btn btn-primary" disabled>Disabled</button>
                <button class="btn btn-primary" onclick="UI.showToast('Button clicked!', 'success')">
                    <span class="spinner-border spinner-sm" style="display: none;"></span>
                    Click Me
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Alerts Demo -->
<div class="card mb-4">
    <div class="card-header">
        <h3 class="card-title">Alerts</h3>
    </div>
    <div class="card-body">
        <div class="alert alert-success alert-dismissible">
            <strong>Success!</strong> Operacioni u krye me sukses.
            <button class="alert-close" onclick="this.parentElement.remove()">×</button>
        </div>
        
        <div class="alert alert-error alert-dismissible">
            <strong>Error!</strong> Ka ndodhur një gabim gjatë procesimit.
            <button class="alert-close" onclick="this.parentElement.remove()">×</button>
        </div>
        
        <div class="alert alert-warning alert-dismissible">
            <strong>Warning!</strong> Kjo veprim mund të jetë i rrezikshëm.
            <button class="alert-close" onclick="this.parentElement.remove()">×</button>
        </div>
        
        <div class="alert alert-info">
            <strong>Info!</strong> Informacion i rëndësishëm për përdoruesin.
        </div>
        
        <div class="mt-3">
            <button class="btn btn-success" onclick="UI.showToast('Success toast!', 'success')">Show Success Toast</button>
            <button class="btn btn-danger" onclick="UI.showToast('Error toast!', 'error')">Show Error Toast</button>
            <button class="btn btn-warning" onclick="UI.showToast('Warning toast!', 'warning')">Show Warning Toast</button>
        </div>
    </div>
</div>

<!-- Badges and Progress Demo -->
<div class="row mb-4">
    <div class="col-6">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Badges</h3>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <h5>Status Badges</h5>
                    <div class="d-flex gap-2 mb-2">
                        <span class="badge badge-primary">Primary</span>
                        <span class="badge badge-success">Success</span>
                        <span class="badge badge-danger">Danger</span>
                        <span class="badge badge-warning">Warning</span>
                        <span class="badge badge-info">Info</span>
                        <span class="badge badge-light">Light</span>
                    </div>
                </div>
                
                <div>
                    <h5>Status Indicators</h5>
                    <div class="d-flex align-center gap-3">
                        <span><span class="status-dot status-online"></span>Online</span>
                        <span><span class="status-dot status-busy"></span>Busy</span>
                        <span><span class="status-dot status-away"></span>Away</span>
                        <span><span class="status-dot status-offline"></span>Offline</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-6">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Progress Bars</h3>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label>Progress 25%</label>
                    <div class="progress">
                        <div class="progress-bar" style="width: 25%"></div>
                    </div>
                </div>
                
                <div class="mb-3">
                    <label>Progress 50%</label>
                    <div class="progress">
                        <div class="progress-bar" style="width: 50%"></div>
                    </div>
                </div>
                
                <div class="mb-3">
                    <label>Progress 75%</label>
                    <div class="progress">
                        <div class="progress-bar" style="width: 75%"></div>
                    </div>
                </div>
                
                <div class="mb-3">
                    <label>Small Progress</label>
                    <div class="progress progress-sm">
                        <div class="progress-bar" style="width: 60%"></div>
                    </div>
                </div>
                
                <div>
                    <label>Large Progress</label>
                    <div class="progress progress-lg">
                        <div class="progress-bar" style="width: 80%"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Forms Demo -->
<div class="card mb-4">
    <div class="card-header">
        <h3 class="card-title">Enhanced Forms</h3>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-6">
                <h5>Standard Forms</h5>
                <div class="form-group">
                    <label>Email Address</label>
                    <input type="email" placeholder="Enter your email">
                </div>
                
                <div class="form-group">
                    <label>Password</label>
                    <input type="password" placeholder="Enter password">
                </div>
                
                <div class="form-group">
                    <label>Select Option</label>
                    <select>
                        <option>Choose an option</option>
                        <option>Option 1</option>
                        <option>Option 2</option>
                    </select>
                </div>
            </div>
            
            <div class="col-6">
                <h5>Floating Label Forms</h5>
                <div class="form-group floating">
                    <input type="text" placeholder=" ">
                    <label>Full Name</label>
                </div>
                
                <div class="form-group floating">
                    <input type="email" placeholder=" ">
                    <label>Email Address</label>
                </div>
                
                <div class="form-group floating">
                    <textarea placeholder=" " rows="3"></textarea>
                    <label>Message</label>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Interactive Demo -->
<div class="card mb-4">
    <div class="card-header">
        <h3 class="card-title">Interactive Components</h3>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-6">
                <h5>Tooltips</h5>
                <p>
                    <span class="tooltip" data-tooltip="This is a tooltip!">Hover over me</span> për të parë tooltip.
                </p>
                <p>
                    <button class="btn btn-primary tooltip" data-tooltip="Button tooltip">Button me Tooltip</button>
                </p>
            </div>
            
            <div class="col-6">
                <h5>Loading States</h5>
                <div class="d-flex gap-2 mb-3">
                    <div class="spinner-border text-primary"></div>
                    <div class="spinner-border spinner-sm text-success"></div>
                    <div class="spinner-border spinner-lg text-danger"></div>
                </div>
                
                <button class="btn btn-primary" onclick="showLoadingDemo()">Show Loading Overlay</button>
                <button class="btn btn-danger" onclick="showConfirmDemo()">Show Confirm Dialog</button>
            </div>
        </div>
    </div>
</div>

<script>
function showLoadingDemo() {
    const overlay = UI.showLoadingOverlay();
    setTimeout(() => {
        UI.hideLoadingOverlay();
        UI.showToast('Loading completed!', 'success');
    }, 2000);
}

function showConfirmDemo() {
    UI.confirm('Are you sure you want to proceed?', function() {
        UI.showToast('Action confirmed!', 'success');
    });
}
</script>
{% endblock %}
