from django.contrib import admin
from django.utils.html import format_html
from .models import Invoice, InvoiceItem, Payment


class InvoiceItemInline(admin.TabularInline):
    """Inline admin for invoice items."""
    model = InvoiceItem
    extra = 0
    readonly_fields = ('amount',)
    fields = ('case', 'description', 'quantity', 'unit_price', 'amount')


class PaymentInline(admin.TabularInline):
    """Inline admin for payments."""
    model = Payment
    extra = 0
    fields = ('amount', 'payment_method', 'reference_number', 'payment_date', 'notes')


@admin.register(Invoice)
class InvoiceAdmin(admin.ModelAdmin):
    """Admin for invoices."""
    
    list_display = ('invoice_number', 'clinic', 'status', 'total_amount', 'due_date_indicator', 'created_at')
    list_filter = ('status', 'issue_date', 'due_date', 'created_at')
    search_fields = ('invoice_number', 'clinic__clinic_name', 'clinic__nipt')
    readonly_fields = ('invoice_number', 'issue_date', 'created_at', 'updated_at')
    
    fieldsets = (
        ('Informacioni Themelor', {
            'fields': ('invoice_number', 'clinic', 'status')
        }),
        ('Datat', {
            'fields': ('issue_date', 'due_date')
        }),
        ('Shumat', {
            'fields': ('total_amount', 'tax_amount')
        }),
        ('Detaje', {
            'fields': ('notes', 'pdf_file')
        }),
        ('Timestamping', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    inlines = [InvoiceItemInline, PaymentInline]
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('clinic').prefetch_related('items', 'payments')
    
    def due_date_indicator(self, obj):
        """Show due date with overdue indicator."""
        if obj.is_overdue():
            return format_html(
                '<span style="color: red; font-weight: bold;">⚠️ {}</span>',
                obj.due_date.strftime('%Y-%m-%d')
            )
        elif obj.status == 'PAID':
            return format_html(
                '<span style="color: green;">✅ {}</span>',
                obj.due_date.strftime('%Y-%m-%d')
            )
        else:
            return format_html(
                '<span>{}</span>',
                obj.due_date.strftime('%Y-%m-%d')
            )
    
    due_date_indicator.short_description = "Data e Scadencës"
    due_date_indicator.admin_order_field = 'due_date'
    
    def save_model(self, request, obj, form, change):
        """Override save to recalculate total."""
        super().save_model(request, obj, form, change)
        # Recalculate total from items
        obj.total_amount = obj.calculate_total()
        obj.save()


@admin.register(InvoiceItem)
class InvoiceItemAdmin(admin.ModelAdmin):
    """Admin for invoice items."""
    
    list_display = ('invoice', 'case', 'description', 'quantity', 'unit_price', 'amount')
    list_filter = ('invoice__status', 'case__product')
    search_fields = ('invoice__invoice_number', 'case__case_id', 'description')
    readonly_fields = ('amount',)
    
    fieldsets = (
        ('Informacioni Themelor', {
            'fields': ('invoice', 'case', 'description')
        }),
        ('Çmimet', {
            'fields': ('quantity', 'unit_price', 'amount')
        }),
    )
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('invoice', 'case')


@admin.register(Payment)
class PaymentAdmin(admin.ModelAdmin):
    """Admin for payments."""
    
    list_display = ('invoice', 'amount', 'payment_method', 'payment_date', 'reference_number')
    list_filter = ('payment_method', 'payment_date')
    search_fields = ('invoice__invoice_number', 'reference_number')
    readonly_fields = ('created_at',)
    
    fieldsets = (
        ('Informacioni Themelor', {
            'fields': ('invoice', 'amount', 'payment_method', 'payment_date')
        }),
        ('Detaje', {
            'fields': ('reference_number', 'notes')
        }),
        ('Timestamping', {
            'fields': ('created_at',)
        }),
    )
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('invoice')
