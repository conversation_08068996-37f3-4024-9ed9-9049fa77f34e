<!DOCTYPE html>
<html>
<head>
    <title>Test Kanban API</title>
</head>
<body>
    <h1>Test Kanban API</h1>
    <button onclick="testAPI()">Test Kanban API</button>
    <div id="result"></div>
    
    <script>
        async function testAPI() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = 'Loading...';
            
            try {
                const response = await fetch('/api/cases/kanban_data/');
                const data = await response.json();
                resultDiv.innerHTML = '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
            } catch (error) {
                resultDiv.innerHTML = 'Error: ' + error.message;
            }
        }
    </script>
</body>
</html>
