from django.contrib.auth.models import AbstractUser
from django.db import models


class CustomUser(AbstractUser):
    """
    Custom user model that extends Django's AbstractUser.
    Uses email as the primary identifier instead of username.
    """
    
    ROLE_CHOICES = (
        ('<PERSON><PERSON><PERSON>', 'Administrator'),
        ('<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>'),
        ('TECHNICIAN', 'Teknik'),
        ('FINANCE', 'Financë'),
        ('COURIER', 'Postier'),
        ('CLIENT', 'Klient'),
    )
    
    email = models.EmailField(unique=True, verbose_name="Email")
    role = models.Char<PERSON><PERSON>(max_length=20, choices=ROLE_CHOICES, verbose_name="<PERSON><PERSON>")
    is_active = models.BooleanField(default=False, verbose_name="Aktiv")
    
    # Make username optional and not unique
    username = models.Char<PERSON>ield(
        max_length=150,
        blank=True,
        null=True,
        verbose_name="Username"
    )
    
    # Use email as the username field
    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = []  # Remove username from required fields
    
    class Meta:
        verbose_name = "<PERSON>ërdorues"
        verbose_name_plural = "P<PERSON>rdorues"
        
    def __str__(self):
        return f"{self.email} ({self.get_role_display()})"
    
    def save(self, *args, **kwargs):
        """Override save to set username to email if not provided."""
        if not self.username:
            self.username = self.email
        super().save(*args, **kwargs)
    
    def get_full_name(self):
        """Return the full name of the user."""
        return f"{self.first_name} {self.last_name}".strip()
    
    def is_clinic(self):
        """Check if user is a clinic client."""
        return self.role == 'CLIENT'
    
    def is_staff_member(self):
        """Check if user is a staff member (not client)."""
        return self.role in ['ADMIN', 'MANAGER', 'TECHNICIAN', 'FINANCE', 'COURIER']


class ClinicProfile(models.Model):
    """
    Profile model for clinic clients.
    Contains additional information specific to dental clinics.
    """
    
    user = models.OneToOneField(
        CustomUser, 
        on_delete=models.CASCADE, 
        primary_key=True,
        related_name='clinic_profile',
        verbose_name="Përdorues"
    )
    clinic_name = models.CharField(max_length=255, verbose_name="Emri i Klinikës")
    nipt = models.CharField(max_length=20, unique=True, verbose_name="NIPT")
    address = models.TextField(verbose_name="Adresa")
    contact_person = models.CharField(max_length=255, verbose_name="Personi i Kontaktit")
    phone_number = models.CharField(max_length=20, verbose_name="Numri i Telefonit")
    
    class Meta:
        verbose_name = "Profili i Klinikës"
        verbose_name_plural = "Profilet e Klinikave"
        
    def __str__(self):
        return f"{self.clinic_name} (NIPT: {self.nipt})"


class TechnicianProfile(models.Model):
    """
    Profile model for technician staff.
    Contains additional information specific to dental technicians.
    """
    
    user = models.OneToOneField(
        CustomUser,
        on_delete=models.CASCADE,
        primary_key=True,
        related_name='technician_profile',
        verbose_name="Përdorues"
    )
    specialization = models.CharField(
        max_length=100, 
        verbose_name="Specializimi",
        help_text="P.sh., 'CAD/CAM', 'Ceramist', 'Prosthetics'"
    )
    
    class Meta:
        verbose_name = "Profili i Teknikut"
        verbose_name_plural = "Profilet e Teknikëve"
        
    def __str__(self):
        return f"{self.user.get_full_name()} - {self.specialization}"
