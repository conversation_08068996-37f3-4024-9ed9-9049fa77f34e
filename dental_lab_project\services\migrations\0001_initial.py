# Generated by Django 4.2.7 on 2025-07-17 11:44

from decimal import Decimal
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("inventory", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="FinalProduct",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        help_text="P.sh., 'Kurorë Zirkoni Estetike'",
                        max_length=255,
                        verbose_name="Emri i Produktit",
                    ),
                ),
                (
                    "description",
                    models.TextField(
                        blank=True,
                        help_text="Përshkrim i detajuar i produktit",
                        verbose_name="Përshkrimi",
                    ),
                ),
                (
                    "base_price",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="Çmimi bazë i faturimit në Lekë",
                        max_digits=10,
                        validators=[
                            django.core.validators.MinValueValidator(Decimal("0.01"))
                        ],
                        verbose_name="Çmimi Bazë",
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(
                        default=True,
                        help_text="Nëse produkti është aktiv dhe mund të porositet",
                        verbose_name="Aktiv",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Krijuar më"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Përditësuar më"),
                ),
            ],
            options={
                "verbose_name": "Produkt Final",
                "verbose_name_plural": "Produktet Finale",
                "ordering": ["name"],
            },
        ),
        migrations.CreateModel(
            name="RecipeItem",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "quantity",
                    models.DecimalField(
                        decimal_places=3,
                        help_text="Sasia standarde që nevojitet për këtë produkt",
                        max_digits=10,
                        validators=[
                            django.core.validators.MinValueValidator(Decimal("0.001"))
                        ],
                        verbose_name="Sasia",
                    ),
                ),
                (
                    "notes",
                    models.TextField(
                        blank=True,
                        help_text="Shënime shtesë për përdorimin e materialit",
                        verbose_name="Shënime",
                    ),
                ),
                (
                    "material",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="recipe_uses",
                        to="inventory.rawmaterial",
                        verbose_name="Materiali",
                    ),
                ),
                (
                    "product",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="recipe_items",
                        to="services.finalproduct",
                        verbose_name="Produkti",
                    ),
                ),
            ],
            options={
                "verbose_name": "Përbërës i Recepturës",
                "verbose_name_plural": "Përbërësit e Recepturës",
                "ordering": ["product", "material"],
                "unique_together": {("product", "material")},
            },
        ),
    ]
