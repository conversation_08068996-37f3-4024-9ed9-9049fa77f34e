# Generated by Django 4.2.7 on 2025-07-17 11:40

from django.conf import settings
import django.contrib.auth.models
import django.contrib.auth.validators
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("auth", "0012_alter_user_first_name_max_length"),
    ]

    operations = [
        migrations.CreateModel(
            name="CustomUser",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("password", models.CharField(max_length=128, verbose_name="password")),
                (
                    "last_login",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="last login"
                    ),
                ),
                (
                    "is_superuser",
                    models.BooleanField(
                        default=False,
                        help_text="Designates that this user has all permissions without explicitly assigning them.",
                        verbose_name="superuser status",
                    ),
                ),
                (
                    "username",
                    models.Char<PERSON>ield(
                        error_messages={
                            "unique": "A user with that username already exists."
                        },
                        help_text="Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only.",
                        max_length=150,
                        unique=True,
                        validators=[
                            django.contrib.auth.validators.UnicodeUsernameValidator()
                        ],
                        verbose_name="username",
                    ),
                ),
                (
                    "first_name",
                    models.CharField(
                        blank=True, max_length=150, verbose_name="first name"
                    ),
                ),
                (
                    "last_name",
                    models.CharField(
                        blank=True, max_length=150, verbose_name="last name"
                    ),
                ),
                (
                    "is_staff",
                    models.BooleanField(
                        default=False,
                        help_text="Designates whether the user can log into this admin site.",
                        verbose_name="staff status",
                    ),
                ),
                (
                    "date_joined",
                    models.DateTimeField(
                        default=django.utils.timezone.now, verbose_name="date joined"
                    ),
                ),
                (
                    "email",
                    models.EmailField(
                        max_length=254, unique=True, verbose_name="Email"
                    ),
                ),
                (
                    "role",
                    models.CharField(
                        choices=[
                            ("ADMIN", "Administrator"),
                            ("MANAGER", "Menaxher"),
                            ("TECHNICIAN", "Teknik"),
                            ("FINANCE", "Financë"),
                            ("COURIER", "Postier"),
                            ("CLIENT", "Klient"),
                        ],
                        max_length=20,
                        verbose_name="Roli",
                    ),
                ),
                ("is_active", models.BooleanField(default=False, verbose_name="Aktiv")),
                (
                    "groups",
                    models.ManyToManyField(
                        blank=True,
                        help_text="The groups this user belongs to. A user will get all permissions granted to each of their groups.",
                        related_name="user_set",
                        related_query_name="user",
                        to="auth.group",
                        verbose_name="groups",
                    ),
                ),
                (
                    "user_permissions",
                    models.ManyToManyField(
                        blank=True,
                        help_text="Specific permissions for this user.",
                        related_name="user_set",
                        related_query_name="user",
                        to="auth.permission",
                        verbose_name="user permissions",
                    ),
                ),
            ],
            options={
                "verbose_name": "Përdorues",
                "verbose_name_plural": "Përdorues",
            },
            managers=[
                ("objects", django.contrib.auth.models.UserManager()),
            ],
        ),
        migrations.CreateModel(
            name="ClinicProfile",
            fields=[
                (
                    "user",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        primary_key=True,
                        related_name="clinic_profile",
                        serialize=False,
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Përdorues",
                    ),
                ),
                (
                    "clinic_name",
                    models.CharField(max_length=255, verbose_name="Emri i Klinikës"),
                ),
                (
                    "nipt",
                    models.CharField(max_length=20, unique=True, verbose_name="NIPT"),
                ),
                ("address", models.TextField(verbose_name="Adresa")),
                (
                    "contact_person",
                    models.CharField(
                        max_length=255, verbose_name="Personi i Kontaktit"
                    ),
                ),
                (
                    "phone_number",
                    models.CharField(max_length=20, verbose_name="Numri i Telefonit"),
                ),
            ],
            options={
                "verbose_name": "Profili i Klinikës",
                "verbose_name_plural": "Profilet e Klinikave",
            },
        ),
        migrations.CreateModel(
            name="TechnicianProfile",
            fields=[
                (
                    "user",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        primary_key=True,
                        related_name="technician_profile",
                        serialize=False,
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Përdorues",
                    ),
                ),
                (
                    "specialization",
                    models.CharField(
                        help_text="P.sh., 'CAD/CAM', 'Ceramist', 'Prosthetics'",
                        max_length=100,
                        verbose_name="Specializimi",
                    ),
                ),
            ],
            options={
                "verbose_name": "Profili i Teknikut",
                "verbose_name_plural": "Profilet e Teknikëve",
            },
        ),
    ]
