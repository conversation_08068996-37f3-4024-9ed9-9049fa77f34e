from django.db import models
from django.contrib.auth import get_user_model

User = get_user_model()


class CaseMessage(models.Model):
    """
    Model for messages between lab staff and clients for specific cases.
    Provides a chat-like interface for case-specific communication.
    """
    
    case = models.ForeignKey(
        'cases.Case',
        on_delete=models.CASCADE,
        related_name='messages',
        verbose_name="Po<PERSON>ia"
    )
    sender = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='sent_messages',
        verbose_name="Dërguesi"
    )
    message = models.TextField(
        verbose_name="Mesazhi"
    )
    attachment = models.FileField(
        upload_to='message_attachments/%Y/%m/%d/',
        blank=True,
        null=True,
        verbose_name="Bashkëngjitje"
    )
    is_read = models.BooleanField(
        default=False,
        verbose_name="Lexuar"
    )
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name="<PERSON><PERSON>juar më"
    )
    
    class Meta:
        verbose_name = "Mesazhi i Porosisë"
        verbose_name_plural = "Mesazhet e Porosisë"
        ordering = ['created_at']
        
    def __str__(self):
        return f"{self.case.case_id} - {self.sender.get_full_name()}: {self.message[:50]}..."
    
    def mark_as_read(self):
        """Mark message as read."""
        self.is_read = True
        self.save()


class Notification(models.Model):
    """
    Model for system notifications to users.
    Handles both in-app and email notifications.
    """
    
    NOTIFICATION_TYPES = [
        ('STATUS_CHANGE', 'Ndryshim Statusi'),
        ('ASSIGNMENT', 'Caktim'),
        ('APPROVAL_REQUIRED', 'Kërkon Miratim'),
        ('APPROVAL_RESPONSE', 'Përgjigje Miratimi'),
        ('INVOICE_GENERATED', 'Faturë e Gjeneruar'),
        ('PAYMENT_RECEIVED', 'Pagesë e Pranuar'),
        ('LOW_STOCK', 'Stok i Ulët'),
        ('CASE_OVERDUE', 'Porosi e Vonuar'),
        ('SYSTEM', 'Sistem'),
    ]
    
    recipient = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='notifications',
        verbose_name="Marrësi"
    )
    notification_type = models.CharField(
        max_length=20,
        choices=NOTIFICATION_TYPES,
        verbose_name="Lloji"
    )
    title = models.CharField(
        max_length=255,
        verbose_name="Titulli"
    )
    message = models.TextField(
        verbose_name="Mesazhi"
    )
    case = models.ForeignKey(
        'cases.Case',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='notifications',
        verbose_name="Porosia"
    )
    is_read = models.BooleanField(
        default=False,
        verbose_name="Lexuar"
    )
    is_email_sent = models.BooleanField(
        default=False,
        verbose_name="Email u Dërgua"
    )
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name="Krijuar më"
    )
    
    class Meta:
        verbose_name = "Njoftim"
        verbose_name_plural = "Njoftimet"
        ordering = ['-created_at']
        
    def __str__(self):
        return f"{self.recipient.get_full_name()} - {self.title}"
    
    def mark_as_read(self):
        """Mark notification as read."""
        self.is_read = True
        self.save()
