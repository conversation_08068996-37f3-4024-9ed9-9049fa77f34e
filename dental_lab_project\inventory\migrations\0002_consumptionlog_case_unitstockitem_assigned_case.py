# Generated by Django 4.2.7 on 2025-07-17 11:49

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("cases", "0001_initial"),
        ("inventory", "0001_initial"),
    ]

    operations = [
        migrations.AddField(
            model_name="consumptionlog",
            name="case",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="material_consumption",
                to="cases.case",
                verbose_name="Porosia",
            ),
        ),
        migrations.AddField(
            model_name="unitstockitem",
            name="assigned_case",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="assigned_materials",
                to="cases.case",
                verbose_name="Porosia e Caktuar",
            ),
        ),
    ]
