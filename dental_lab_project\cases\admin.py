from django.contrib import admin
from django.utils.html import format_html
from django.utils import timezone
from .models import Case, CaseFile, CaseLog


class CaseFileInline(admin.TabularInline):
    """Inline admin for case files."""
    model = CaseFile
    extra = 0
    readonly_fields = ('uploaded_at', 'uploaded_by')
    fields = ('file', 'file_type', 'description', 'uploaded_by', 'uploaded_at')
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('uploaded_by')


class CaseLogInline(admin.TabularInline):
    """Inline admin for case logs."""
    model = CaseLog
    extra = 0
    readonly_fields = ('action', 'description', 'user', 'timestamp')
    fields = ('action', 'description', 'user', 'timestamp')
    can_delete = False
    
    def has_add_permission(self, request, obj=None):
        return False
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user').order_by('-timestamp')


@admin.register(Case)
class CaseAdmin(admin.ModelAdmin):
    """Admin for cases."""
    
    list_display = ('case_id', 'patient_name', 'clinic', 'product', 'status', 'priority', 'due_date_indicator', 'assigned_technician', 'created_at')
    list_filter = ('status', 'priority', 'creation_type', 'assigned_technician', 'due_date', 'created_at')
    search_fields = ('case_id', 'patient_name', 'clinic__clinic_name', 'product__name', 'notes')
    readonly_fields = ('case_id', 'created_at', 'updated_at')
    
    fieldsets = (
        ('Informacioni Themelor', {
            'fields': ('case_id', 'clinic', 'patient_name', 'product', 'creation_type')
        }),
        ('Statusi dhe Caktimi', {
            'fields': ('status', 'priority', 'assigned_technician', 'due_date')
        }),
        ('Detajet e Punës', {
            'fields': ('color_shade', 'special_instructions', 'notes')
        }),
        ('Dërgimi', {
            'fields': ('shipped_tracking_number',),
            'classes': ('collapse',)
        }),
        ('Timestamping', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    inlines = [CaseFileInline, CaseLogInline]
    
    def get_queryset(self, request):
        """Optimize queries with select_related."""
        return super().get_queryset(request).select_related(
            'clinic', 
            'product', 
            'assigned_technician'
        ).prefetch_related('files', 'logs')
    
    def due_date_indicator(self, obj):
        """Show due date with color indicator."""
        days_until_due = obj.get_days_until_due()
        
        if obj.is_overdue():
            return format_html(
                '<span style="color: red; font-weight: bold;">⚠️ {}</span>',
                obj.due_date.strftime('%Y-%m-%d')
            )
        elif days_until_due <= 1:
            return format_html(
                '<span style="color: orange; font-weight: bold;">🟡 {}</span>',
                obj.due_date.strftime('%Y-%m-%d')
            )
        elif days_until_due <= 3:
            return format_html(
                '<span style="color: #ff9900;">🟠 {}</span>',
                obj.due_date.strftime('%Y-%m-%d')
            )
        else:
            return format_html(
                '<span style="color: green;">✅ {}</span>',
                obj.due_date.strftime('%Y-%m-%d')
            )
    
    due_date_indicator.short_description = "Data e Dorëzimit"
    due_date_indicator.admin_order_field = 'due_date'
    
    def save_model(self, request, obj, form, change):
        """Override save to create logs."""
        if change:
            # Log status changes
            original = Case.objects.get(pk=obj.pk)
            if original.status != obj.status:
                CaseLog.objects.create(
                    case=obj,
                    action='STATUS_CHANGED',
                    description=f"Statusi u ndryshua nga '{original.get_status_display()}' në '{obj.get_status_display()}'",
                    user=request.user
                )
            
            # Log technician assignment
            if original.assigned_technician != obj.assigned_technician:
                if obj.assigned_technician:
                    CaseLog.objects.create(
                        case=obj,
                        action='ASSIGNED',
                        description=f"U caktua tek tekniku: {obj.assigned_technician.user.get_full_name()}",
                        user=request.user
                    )
        else:
            # Log creation
            CaseLog.objects.create(
                case=obj,
                action='CREATED',
                description=f"Porosia u krijua nga {request.user.get_full_name()}",
                user=request.user
            )
        
        super().save_model(request, obj, form, change)


@admin.register(CaseFile)
class CaseFileAdmin(admin.ModelAdmin):
    """Admin for case files."""
    
    list_display = ('case', 'file', 'file_type', 'description', 'uploaded_by', 'uploaded_at')
    list_filter = ('file_type', 'uploaded_at')
    search_fields = ('case__case_id', 'case__patient_name', 'description')
    readonly_fields = ('uploaded_at',)
    
    fieldsets = (
        ('Informacioni Themelor', {
            'fields': ('case', 'file', 'file_type', 'description')
        }),
        ('Metadata', {
            'fields': ('uploaded_by', 'uploaded_at')
        }),
    )
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('case', 'uploaded_by')
    
    def save_model(self, request, obj, form, change):
        """Set uploaded_by to current user."""
        if not change:
            obj.uploaded_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(CaseLog)
class CaseLogAdmin(admin.ModelAdmin):
    """Admin for case logs."""
    
    list_display = ('case', 'action', 'description', 'user', 'timestamp')
    list_filter = ('action', 'timestamp')
    search_fields = ('case__case_id', 'description', 'user__email')
    readonly_fields = ('case', 'action', 'description', 'user', 'timestamp')
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('case', 'user')
    
    def has_add_permission(self, request):
        """Case logs are created automatically."""
        return False
    
    def has_change_permission(self, request, obj=None):
        """Case logs should be read-only."""
        return False
    
    def has_delete_permission(self, request, obj=None):
        """Case logs should not be deleted."""
        return False
