from rest_framework.permissions import BasePermission


class IsLabStaff(BasePermission):
    """
    Permission that allows access only to lab staff members.
    Lab staff includes: ADMIN, MANAGER, TECHNICIAN, FINANCE, COURIER
    """
    
    def has_permission(self, request, view):
        return (
            request.user.is_authenticated and
            request.user.is_staff_member()
        )


class IsClientOwner(BasePermission):
    """
    Permission that allows access only to the client who owns the object.
    """
    
    def has_object_permission(self, request, view, obj):
        if not request.user.is_authenticated:
            return False
        
        if request.user.is_clinic():
            # Check if the object belongs to the client's clinic
            if hasattr(obj, 'clinic'):
                return obj.clinic == request.user.clinic_profile
            elif hasattr(obj, 'case'):
                return obj.case.clinic == request.user.clinic_profile
        
        return False


class IsOwnerOrLabStaff(BasePermission):
    """
    Permission that allows access to lab staff or the client who owns the object.
    """
    
    def has_permission(self, request, view):
        return request.user.is_authenticated
    
    def has_object_permission(self, request, view, obj):
        if not request.user.is_authenticated:
            return False
        
        # Lab staff can access everything
        if request.user.is_staff_member():
            return True
        
        # Clients can only access their own objects
        if request.user.is_clinic():
            if hasattr(obj, 'clinic'):
                return obj.clinic == request.user.clinic_profile
            elif hasattr(obj, 'case'):
                return obj.case.clinic == request.user.clinic_profile
        
        return False


class IsAssignedTechnician(BasePermission):
    """
    Permission that allows access only to the assigned technician.
    """
    
    def has_object_permission(self, request, view, obj):
        if not request.user.is_authenticated:
            return False
        
        if request.user.role == 'TECHNICIAN':
            if hasattr(obj, 'assigned_technician'):
                return (
                    obj.assigned_technician and
                    obj.assigned_technician.user == request.user
                )
        
        return False


class IsAdminOrManager(BasePermission):
    """
    Permission that allows access only to admins and managers.
    """
    
    def has_permission(self, request, view):
        return (
            request.user.is_authenticated and
            request.user.role in ['ADMIN', 'MANAGER']
        )


class CanManageInventory(BasePermission):
    """
    Permission for inventory management.
    Only admins, managers, and technicians can manage inventory.
    """
    
    def has_permission(self, request, view):
        return (
            request.user.is_authenticated and
            request.user.role in ['ADMIN', 'MANAGER', 'TECHNICIAN']
        )


class CanManageFinance(BasePermission):
    """
    Permission for financial operations.
    Only admins, managers, and finance staff can manage finances.
    """
    
    def has_permission(self, request, view):
        return (
            request.user.is_authenticated and
            request.user.role in ['ADMIN', 'MANAGER', 'FINANCE']
        )


class IsReadOnlyOrLabStaff(BasePermission):
    """
    Permission that allows read-only access to everyone and write access to lab staff.
    """
    
    def has_permission(self, request, view):
        if not request.user.is_authenticated:
            return False
        
        # Read permissions for any authenticated user
        if request.method in ['GET', 'HEAD', 'OPTIONS']:
            return True
        
        # Write permissions only for lab staff
        return request.user.is_staff_member()
