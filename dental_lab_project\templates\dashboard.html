{% extends 'base.html' %}

{% block title %}Dashboard - Dental Lab Management System{% endblock %}

{% block content %}
<div id="authStatus" class="user-info" style="display: none;">
    <strong>Welcome back!</strong> You are logged in as <span id="currentUserEmail"></span>
</div>

<div style="text-align: center; margin-bottom: 30px;">
    <h1 style="color: #2c3e50; font-size: 2.5em; margin-bottom: 10px;">🦷 Dental Lab Management System</h1>
    <p style="color: #7f8c8d; font-size: 1.2em;">API Dashboard & Testing Interface</p>
</div>

<div style="background: white; padding: 20px; border-radius: 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); margin-bottom: 20px;">
    <h2 style="color: #2c3e50; margin-bottom: 15px;">API Test</h2>
    <button class="btn" onclick="testAPI()" style="margin-right: 10px;">Test API Connection</button>
    <button class="btn" onclick="testCasesAPI()" style="margin-right: 10px;">Test Cases API</button>
    <button class="btn" onclick="testKanbanAPI()">Test Kanban API</button>
    
    <div id="api-result" style="display: none; background: #f8f9fa; padding: 15px; border-radius: 5px; margin-top: 15px; border: 1px solid #e9ecef;">
        <h3>API Response:</h3>
        <pre id="api-response" style="background: white; padding: 10px; border-radius: 3px; overflow-x: auto;"></pre>
    </div>
</div>

<div style="background: white; padding: 20px; border-radius: 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
    <h2 style="color: #2c3e50; margin-bottom: 15px;">Available API Endpoints</h2>
    
    <div style="background: #f8f9fa; padding: 10px; border-radius: 5px; margin-bottom: 10px; border-left: 4px solid #3498db;">
        <span style="display: inline-block; background: #27ae60; color: white; padding: 2px 8px; border-radius: 3px; font-size: 12px; margin-right: 10px;">GET</span>
        <strong>/api/test/</strong> - Test API connection
    </div>
    
    <div style="background: #f8f9fa; padding: 10px; border-radius: 5px; margin-bottom: 10px; border-left: 4px solid #3498db;">
        <span style="display: inline-block; background: #27ae60; color: white; padding: 2px 8px; border-radius: 3px; font-size: 12px; margin-right: 10px;">GET</span>
        <strong>/api/cases/</strong> - Get all cases
    </div>
    
    <div style="background: #f8f9fa; padding: 10px; border-radius: 5px; margin-bottom: 10px; border-left: 4px solid #3498db;">
        <span style="display: inline-block; background: #e74c3c; color: white; padding: 2px 8px; border-radius: 3px; font-size: 12px; margin-right: 10px;">POST</span>
        <strong>/api/cases/</strong> - Create new case
    </div>
    
    <div style="background: #f8f9fa; padding: 10px; border-radius: 5px; margin-bottom: 10px; border-left: 4px solid #3498db;">
        <span style="display: inline-block; background: #27ae60; color: white; padding: 2px 8px; border-radius: 3px; font-size: 12px; margin-right: 10px;">GET</span>
        <strong>/api/cases/{id}/</strong> - Get case details
    </div>
    
    <div style="background: #f8f9fa; padding: 10px; border-radius: 5px; margin-bottom: 10px; border-left: 4px solid #3498db;">
        <span style="display: inline-block; background: #e74c3c; color: white; padding: 2px 8px; border-radius: 3px; font-size: 12px; margin-right: 10px;">POST</span>
        <strong>/api/cases/{id}/change_status/</strong> - Change case status
    </div>
    
    <div style="background: #f8f9fa; padding: 10px; border-radius: 5px; margin-bottom: 10px; border-left: 4px solid #3498db;">
        <span style="display: inline-block; background: #e74c3c; color: white; padding: 2px 8px; border-radius: 3px; font-size: 12px; margin-right: 10px;">POST</span>
        <strong>/api/cases/{id}/assign_technician/</strong> - Assign technician to case
    </div>
    
    <div style="background: #f8f9fa; padding: 10px; border-radius: 5px; margin-bottom: 10px; border-left: 4px solid #3498db;">
        <span style="display: inline-block; background: #27ae60; color: white; padding: 2px 8px; border-radius: 3px; font-size: 12px; margin-right: 10px;">GET</span>
        <strong>/api/cases/kanban_data/</strong> - Get Kanban board data
    </div>
    
    <div style="background: #f8f9fa; padding: 10px; border-radius: 5px; margin-bottom: 10px; border-left: 4px solid #3498db;">
        <span style="display: inline-block; background: #27ae60; color: white; padding: 2px 8px; border-radius: 3px; font-size: 12px; margin-right: 10px;">GET</span>
        <strong>/api/cases/dashboard_stats/</strong> - Get dashboard statistics
    </div>
    
    <div style="background: #f8f9fa; padding: 10px; border-radius: 5px; margin-bottom: 10px; border-left: 4px solid #3498db;">
        <span style="display: inline-block; background: #27ae60; color: white; padding: 2px 8px; border-radius: 3px; font-size: 12px; margin-right: 10px;">GET</span>
        <strong>/api/case-files/</strong> - Get case files
    </div>
    
    <h3 style="color: #2c3e50; margin: 20px 0 15px 0;">Authentication Endpoints</h3>
    
    <div style="background: #f8f9fa; padding: 10px; border-radius: 5px; margin-bottom: 10px; border-left: 4px solid #9b59b6;">
        <span style="display: inline-block; background: #e74c3c; color: white; padding: 2px 8px; border-radius: 3px; font-size: 12px; margin-right: 10px;">POST</span>
        <strong>/api/auth/login/</strong> - User login
    </div>
    
    <div style="background: #f8f9fa; padding: 10px; border-radius: 5px; margin-bottom: 10px; border-left: 4px solid #9b59b6;">
        <span style="display: inline-block; background: #e74c3c; color: white; padding: 2px 8px; border-radius: 3px; font-size: 12px; margin-right: 10px;">POST</span>
        <strong>/api/auth/logout/</strong> - User logout
    </div>
    
    <div style="background: #f8f9fa; padding: 10px; border-radius: 5px; margin-bottom: 10px; border-left: 4px solid #9b59b6;">
        <span style="display: inline-block; background: #e74c3c; color: white; padding: 2px 8px; border-radius: 3px; font-size: 12px; margin-right: 10px;">POST</span>
        <strong>/api/auth/register/</strong> - User registration
    </div>
    
    <div style="background: #f8f9fa; padding: 10px; border-radius: 5px; margin-bottom: 10px; border-left: 4px solid #9b59b6;">
        <span style="display: inline-block; background: #27ae60; color: white; padding: 2px 8px; border-radius: 3px; font-size: 12px; margin-right: 10px;">GET</span>
        <strong>/api/auth/profile/</strong> - Get user profile
    </div>
    
    <div style="background: #f8f9fa; padding: 10px; border-radius: 5px; margin-bottom: 10px; border-left: 4px solid #9b59b6;">
        <span style="display: inline-block; background: #27ae60; color: white; padding: 2px 8px; border-radius: 3px; font-size: 12px; margin-right: 10px;">GET</span>
        <strong>/api/auth/status/</strong> - Check authentication status
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Check authentication status on page load
window.addEventListener('load', async function() {
    try {
        const response = await fetch('/api/auth/status/');
        const data = await response.json();
        
        if (data.authenticated) {
            document.getElementById('authStatus').style.display = 'block';
            document.getElementById('currentUserEmail').textContent = data.user.email;
        }
    } catch (error) {
        console.error('Auth status check failed:', error);
    }
});

async function testAPI() {
    const resultDiv = document.getElementById('api-result');
    const responseDiv = document.getElementById('api-response');
    
    resultDiv.style.display = 'block';
    responseDiv.textContent = 'Loading...';
    
    try {
        const response = await fetch('/api/test/');
        const data = await response.json();
        responseDiv.textContent = JSON.stringify(data, null, 2);
    } catch (error) {
        responseDiv.textContent = 'Error: ' + error.message;
    }
}

async function testCasesAPI() {
    const resultDiv = document.getElementById('api-result');
    const responseDiv = document.getElementById('api-response');
    
    resultDiv.style.display = 'block';
    responseDiv.textContent = 'Loading...';
    
    try {
        const response = await fetch('/api/cases/');
        const data = await response.json();
        responseDiv.textContent = JSON.stringify(data, null, 2);
    } catch (error) {
        responseDiv.textContent = 'Error: ' + error.message;
    }
}

async function testKanbanAPI() {
    const resultDiv = document.getElementById('api-result');
    const responseDiv = document.getElementById('api-response');
    
    resultDiv.style.display = 'block';
    responseDiv.textContent = 'Loading...';
    
    try {
        const response = await fetch('/api/cases/kanban_data/');
        const data = await response.json();
        responseDiv.textContent = JSON.stringify(data, null, 2);
    } catch (error) {
        responseDiv.textContent = 'Error: ' + error.message;
    }
}
</script>
{% endblock %}
