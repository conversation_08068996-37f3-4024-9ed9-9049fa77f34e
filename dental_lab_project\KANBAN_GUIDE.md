# 🦷 Kanban Dashboard - Udhëzime Përdorimi

## 🎯 Përmbledhje

Kanban Dashboard është një ndërfaqe interaktive për menaxhimin e porosive të laboratorit dentar. Përdoruesit mund të shohin të gjitha porositë e organizuara në kolona sipas statusit dhe të ndryshojnë statusin duke tërhequr e lëshuar kartat.

## 🚀 Si të Filloni

### 1. Startimi i Serverit
```bash
cd C:\GPT4_PROJECTS\dental_lab_project
python manage.py runserver
```

### 2. Aksesi në Dashboard
Hapni shfletuesin dhe shkoni te:
- **Dashboard Kryesor**: http://127.0.0.1:8000/
- **Kanban Dashboard**: http://127.0.0.1:8000/kanban/
- **Test API**: http://127.0.0.1:8000/test-api/

## 🧪 Test Data

Projekti përfshin të dhëna test të krijuara automatikisht:

### Përdorues Test:
- **Admin**: <EMAIL> / admin123
- **Menaxher**: <EMAIL> / manager123
- **Teknikë**:
  - <EMAIL> / tech123 (Aldo Prendi - CAD/CAM)
  - <EMAIL> / tech123 (Maria Koci - Ceramist)
  - <EMAIL> / tech123 (Gent Hoxha - Prosthetics)
- **Klinika**:
  - <EMAIL> / clinic123 (Dental Clinic Alpha)
  - <EMAIL> / clinic123 (Beta Dental Care)
  - <EMAIL> / clinic123 (Gamma Dental Studio)
  - <EMAIL> / clinic123 (Delta Oral Health)

### Data Test
- 15 porosi test me status të ndryshëm
- 7 produkte test (Kurorë Zirkoni, Proteza, etj.)
- 4 klinika test
- 3 teknikë me specializime të ndryshme

## 📋 Funksionalitete Kanban Dashboard

### 🎨 Karakteristika Kryesore:

1. **Pamja Kanban**
   - Kolona për çdo status (Në Pritje Marrjes, Marrë në Lab, Në Dizajn, etj.)
   - Karta për çdo porosi me informacion të plotë
   - Numërues për çdo kolonë

2. **Drag & Drop**
   - Tërhiqni kartat midis kolonave për të ndryshuar statusin
   - Validim automatik i kalimeve të lejueshme
   - Confirmation dhe log automatik

3. **Filtra dhe Kërkime**
   - Kërkim sipas ID, emrit të pacientit ose klinikës
   - Filtrim sipas teknikut të caktuar
   - Filtrim sipas prioritetit

4. **Statistika**
   - Numri total i porosive
   - Porositë me vonesë
   - Porositë në pritje të miratimit
   - Porositë në prodhim

5. **Detaje të Porosisë**
   - Klikoni mbi një kartë për të parë detajet e plota
   - Informacion për produktin, klinikën, technikunin
   - Lista e skedarëve të bashkëngjitur
   - Historiku i ndryshimeve

### 🎯 Indikatorë Vizual:

- **Ngjyra e Kartave**: Çdo karta ka një borderlinë në ngjyrën e statusit
- **Prioriteti**: Etiketë me ngjyrë sipas prioritetit (E Ulët, Normale, E Lartë, Urgjente)
- **Data e Dorëzimit**:
  - 🔥 E kuqe: Me vonesë
  - ⚠️ Portokalli: Deri në 3 ditë
  - 📅 Blu: Normale

## 🔧 API Endpoints

### Kryesore:
- `GET /api/cases/kanban_data/` - Të dhënat për Kanban board
- `GET /api/cases/dashboard_stats/` - Statistikat
- `POST /api/cases/{id}/change_status/` - Ndryshimi i statusit
- `GET /api/cases/{id}/` - Detajet e porosisë

### Test:
- `GET /api/test/` - Test i API-së
- `GET /test-api/` - Ndërfaqe test për API

## 🛠️ Zgjidhja e Problemeve

### Probleme të Mundshme:

1. **"Authentication credentials not provided"**
   - Kjo është normale për API endpoints që kërkojnë autentifikim
   - Kanban dashboard funksionon edhe pa autentifikim për testim

2. **Serveri nuk starton**
   - Sigurohuni që jeni në direktorinë e duhur
   - Kontrolloni nëse porti 8000 është i lirë

3. **Nuk shfaqen të dhëna**
   - Kontrolloni console të browser-it për gabime JavaScript
   - Sigurohuni që serveri është duke u ekzekutuar

4. **Drag & Drop nuk funksionon**
   - Sigurohuni që JavaScript është aktivizuar
   - Kontrolloni për gabime në console

## 📈 Përmirësime të Ardhshme

- Autentifikim i plotë me role-based permissions
- Real-time updates me WebSockets
- Notifikime push
- Export në Excel/PDF
- Integrimi me 3Shape Communicate
- Mobile responsive design

## 🎉 Sukses!

Nëse të gjitha funksionojnë siç duhet, do të keni një Kanban Dashboard plotësisht funksional me:
- ✅ Pamje kolona me statuset
- ✅ Drag & Drop për ndryshimin e statusit
- ✅ Filtra dhe kërkime
- ✅ Modal me detaje të porosisë
- ✅ Statistika në kohë reale
- ✅ Design modern dhe responsive

Enjoy coding! 🚀
