from django.db import models
from django.core.validators import MinValueValidator
from django.contrib.auth import get_user_model
from decimal import Decimal


User = get_user_model()


class RawMaterial(models.Model):
    """
    Model for raw materials used in production.
    Supports hybrid inventory management (unit-based and recipe-based).
    """
    
    MANAGEMENT_TYPE_CHOICES = (
        ('UNIT', 'Për Njësi/Serializuar'),
        ('RECIPE', 'Sipas Recepturës/I Ndashëm'),
    )
    
    name = models.CharField(
        max_length=255,
        verbose_name="Emri i Materialit",
        help_text="P.sh., 'Bllok Zirkoni Ivoclar A2 98x14mm'"
    )
    sku = models.CharField(
        max_length=50,
        unique=True,
        verbose_name="SKU",
        help_text="Kodi unik i produktit"
    )
    description = models.TextField(
        blank=True,
        verbose_name="Përshkrimi"
    )
    management_type = models.CharField(
        max_length=10,
        choices=MANAGEMENT_TYPE_CHOICES,
        verbose_name="Lloji i Menaxhimit",
        help_text="UNIT: Gjurmohet si njësi individuale. RECIPE: Gjurmohet si sasi totale."
    )
    unit_of_measurement = models.CharField(
        max_length=20,
        default="njësi",
        verbose_name="Njësia e Matjes",
        help_text="P.sh., 'njësi', 'gram', 'ml', 'metra'"
    )
    unit_cost = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        validators=[MinValueValidator(Decimal('0.00'))],
        verbose_name="Kostoja për Njësi",
        help_text="Kostoja për njësi në Lekë"
    )
    total_quantity = models.DecimalField(
        max_digits=10,
        decimal_places=3,
        default=Decimal('0.000'),
        validators=[MinValueValidator(Decimal('0.000'))],
        verbose_name="Sasia Totale",
        help_text="Sasia totale në stok (për materialet RECIPE)"
    )
    low_stock_threshold = models.DecimalField(
        max_digits=10,
        decimal_places=3,
        default=Decimal('10.000'),
        validators=[MinValueValidator(Decimal('0.000'))],
        verbose_name="Pragu i Stokut të Ulët",
        help_text="Alarm kur sasia arrin këtë prag"
    )
    supplier = models.CharField(
        max_length=255,
        blank=True,
        verbose_name="Furnizuesi"
    )
    is_active = models.BooleanField(
        default=True,
        verbose_name="Aktiv"
    )
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="Krijuar më")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="Përditësuar më")
    
    class Meta:
        verbose_name = "Lëndë e Parë"
        verbose_name_plural = "Lëndët e Para"
        ordering = ['name']
        
    def __str__(self):
        return f"{self.name} ({self.sku})"
    
    def is_unit_managed(self):
        """Check if this material is managed by units."""
        return self.management_type == 'UNIT'
    
    def is_recipe_managed(self):
        """Check if this material is managed by recipe/quantity."""
        return self.management_type == 'RECIPE'
    
    def get_available_quantity(self):
        """Get available quantity based on management type."""
        if self.is_unit_managed():
            return self.unit_items.filter(status='IN_STOCK').count()
        else:
            return self.total_quantity
    
    def is_low_stock(self):
        """Check if material is low in stock."""
        return self.get_available_quantity() <= self.low_stock_threshold


class UnitStockItem(models.Model):
    """
    Model for individual units of raw materials.
    Used only for materials with management_type='UNIT'.
    """
    
    STATUS_CHOICES = (
        ('IN_STOCK', 'Në Stok'),
        ('IN_USE', 'Në Përdorim'),
        ('CONSUMED', 'Konsumuar'),
        ('REMNANT', 'Mbetje'),
    )
    
    material = models.ForeignKey(
        RawMaterial,
        on_delete=models.CASCADE,
        related_name='unit_items',
        verbose_name="Materiali"
    )
    serial_number = models.CharField(
        max_length=100,
        unique=True,
        verbose_name="Numri Serial",
        help_text="Barkodi ose numri serial unik"
    )
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='IN_STOCK',
        verbose_name="Statusi"
    )
    assigned_case = models.ForeignKey(
        'cases.Case',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='assigned_materials',
        verbose_name="Porosia e Caktuar"
    )
    purchase_date = models.DateField(
        null=True,
        blank=True,
        verbose_name="Data e Blerjes"
    )
    expiry_date = models.DateField(
        null=True,
        blank=True,
        verbose_name="Data e Skadimit"
    )
    notes = models.TextField(
        blank=True,
        verbose_name="Shënime"
    )
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="Krijuar më")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="Përditësuar më")
    
    class Meta:
        verbose_name = "Njësi e Stokut"
        verbose_name_plural = "Njësitë e Stokut"
        ordering = ['material', 'serial_number']
        
    def __str__(self):
        return f"{self.material.name} - {self.serial_number} ({self.get_status_display()})"
    
    def is_available(self):
        """Check if this unit is available for use."""
        return self.status == 'IN_STOCK'
    
    def mark_as_consumed(self, case=None):
        """Mark this unit as consumed."""
        self.status = 'CONSUMED'
        if case:
            self.assigned_case = case
        self.save()


class ConsumptionLog(models.Model):
    """
    Model for tracking all material consumption.
    Records both unit-based and recipe-based consumption.
    """
    
    material_used = models.ForeignKey(
        RawMaterial,
        on_delete=models.CASCADE,
        related_name='consumption_logs',
        verbose_name="Materiali i Përdorur"
    )
    unit_item_used = models.ForeignKey(
        UnitStockItem,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='consumption_logs',
        verbose_name="Njësia e Përdorur"
    )
    quantity_consumed = models.DecimalField(
        max_digits=10,
        decimal_places=3,
        validators=[MinValueValidator(Decimal('0.001'))],
        verbose_name="Sasia e Konsumuar"
    )
    case = models.ForeignKey(
        'cases.Case',
        on_delete=models.CASCADE,
        null=True,  # Allow null temporarily for migration
        blank=True,
        related_name='material_consumption',
        verbose_name="Porosia"
    )
    technician = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='material_consumption',
        verbose_name="Tekniku"
    )
    notes = models.TextField(
        blank=True,
        verbose_name="Shënime"
    )
    timestamp = models.DateTimeField(
        auto_now_add=True,
        verbose_name="Koha e Konsumimit"
    )
    
    class Meta:
        verbose_name = "Regjistri i Konsumimit"
        verbose_name_plural = "Regjistrat e Konsumimit"
        ordering = ['-timestamp']
        
    def __str__(self):
        return f"{self.material_used.name} - {self.quantity_consumed} ({self.timestamp.strftime('%Y-%m-%d %H:%M')})"
    
    def get_cost(self):
        """Calculate the cost of this consumption."""
        if self.material_used.unit_cost:
            return self.material_used.unit_cost * self.quantity_consumed
        return Decimal('0.00')
