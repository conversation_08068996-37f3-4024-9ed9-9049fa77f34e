from django.core.management.base import BaseCommand
from django.db import transaction
from users.models import CustomUser, ClinicProfile, TechnicianProfile
from services.models import FinalProduct, RecipeItem
from inventory.models import RawMaterial, UnitStockItem
from cases.models import Case
from datetime import date, timedelta


class Command(BaseCommand):
    help = 'Setup demo data for the dental lab system'

    def add_arguments(self, parser):
        parser.add_argument(
            '--reset',
            action='store_true',
            help='Reset all data before creating demo data',
        )

    def handle(self, *args, **options):
        if options['reset']:
            self.stdout.write('Resetting all data...')
            self.reset_data()

        self.stdout.write('Creating demo data...')
        
        with transaction.atomic():
            # Create users
            admin_user = self.create_admin_user()
            manager_user = self.create_manager_user()
            tech_user = self.create_technician_user()
            clinic_user = self.create_clinic_user()
            
            # Create products and materials
            self.create_products_and_materials()
            
            # Create some demo cases
            self.create_demo_cases(clinic_user.clinic_profile, tech_user.technician_profile)

        self.stdout.write(
            self.style.SUCCESS('Successfully created demo data!')
        )
        self.stdout.write('Demo login credentials:')
        self.stdout.write('  Admin: <EMAIL> / admin123')
        self.stdout.write('  Manager: <EMAIL> / manager123')
        self.stdout.write('  Technician: <EMAIL> / tech123')
        self.stdout.write('  Clinic: <EMAIL> / clinic123')

    def reset_data(self):
        """Reset all data except superusers."""
        # Keep superusers but delete other data
        CustomUser.objects.filter(is_superuser=False).delete()
        FinalProduct.objects.all().delete()
        RawMaterial.objects.all().delete()
        Case.objects.all().delete()

    def create_admin_user(self):
        """Create admin user."""
        admin, created = CustomUser.objects.get_or_create(
            email='<EMAIL>',
            defaults={
                'first_name': 'Admin',
                'last_name': 'User',
                'role': 'ADMIN',
                'is_active': True,
                'is_staff': True,
                'is_superuser': True,
            }
        )
        if created:
            admin.set_password('admin123')
            admin.save()
            self.stdout.write(f'Created admin user: {admin.email}')
        return admin

    def create_manager_user(self):
        """Create manager user."""
        manager, created = CustomUser.objects.get_or_create(
            email='<EMAIL>',
            defaults={
                'first_name': 'Aldo',
                'last_name': 'Puci',
                'role': 'MANAGER',
                'is_active': True,
                'is_staff': True,
            }
        )
        if created:
            manager.set_password('manager123')
            manager.save()
            self.stdout.write(f'Created manager user: {manager.email}')
        return manager

    def create_technician_user(self):
        """Create technician user."""
        tech, created = CustomUser.objects.get_or_create(
            email='<EMAIL>',
            defaults={
                'first_name': 'Mario',
                'last_name': 'Rossi',
                'role': 'TECHNICIAN',
                'is_active': True,
            }
        )
        if created:
            tech.set_password('tech123')
            tech.save()
            
            # Create technician profile
            TechnicianProfile.objects.get_or_create(
                user=tech,
                defaults={
                    'specialization': 'CAD/CAM & Zirconia'
                }
            )
            self.stdout.write(f'Created technician user: {tech.email}')
        return tech

    def create_clinic_user(self):
        """Create clinic user."""
        clinic, created = CustomUser.objects.get_or_create(
            email='<EMAIL>',
            defaults={
                'first_name': 'Dr. Giuseppe',
                'last_name': 'Bianchi',
                'role': 'CLIENT',
                'is_active': True,
            }
        )
        if created:
            clinic.set_password('clinic123')
            clinic.save()
            
            # Create clinic profile
            ClinicProfile.objects.get_or_create(
                user=clinic,
                defaults={
                    'clinic_name': 'Studio Dentistico Bianchi',
                    'nipt': 'L12345678T',
                    'address': 'Rruga e Elbasanit, Nr. 123, Tiranë',
                    'contact_person': 'Dr. Giuseppe Bianchi',
                    'phone_number': '+355 69 123 4567'
                }
            )
            self.stdout.write(f'Created clinic user: {clinic.email}')
        return clinic

    def create_products_and_materials(self):
        """Create demo products and materials."""
        # Create raw materials
        zirconia_block, _ = RawMaterial.objects.get_or_create(
            sku='ZIR-A2-98',
            defaults={
                'name': 'Bllok Zirkoni Ivoclar A2 98x14mm',
                'management_type': 'UNIT',
                'unit_of_measurement': 'njësi',
                'unit_cost': 4500.00,
                'low_stock_threshold': 5,
                'supplier': 'Ivoclar Vivadent',
                'is_active': True,
            }
        )

        ceramic_powder, _ = RawMaterial.objects.get_or_create(
            sku='CER-PWD-A2',
            defaults={
                'name': 'Pluhur Ceramic A2',
                'management_type': 'RECIPE',
                'unit_of_measurement': 'gram',
                'unit_cost': 15.50,
                'total_quantity': 500.0,
                'low_stock_threshold': 50.0,
                'supplier': 'Vita Zahnfabrik',
                'is_active': True,
            }
        )

        # Create some unit stock items for zirconia blocks
        for i in range(10):
            UnitStockItem.objects.get_or_create(
                serial_number=f'ZIR-A2-{1000 + i}',
                defaults={
                    'material': zirconia_block,
                    'status': 'IN_STOCK',
                    'purchase_date': date.today() - timedelta(days=30),
                }
            )

        # Create final products
        crown_zirconia, _ = FinalProduct.objects.get_or_create(
            name='Kurorë Zirkoni Estetike',
            defaults={
                'description': 'Kurorë e plotë nga zirkonia me shtresë estetike',
                'base_price': 12000.00,
                'is_active': True,
            }
        )

        bridge_3unit, _ = FinalProduct.objects.get_or_create(
            name='Urë 3 Njësi Zirkoni',
            defaults={
                'description': 'Urë e fiksuar 3 njësi nga zirkonia',
                'base_price': 30000.00,
                'is_active': True,
            }
        )

        # Create recipes
        RecipeItem.objects.get_or_create(
            product=crown_zirconia,
            material=zirconia_block,
            defaults={'quantity': 1.0, 'notes': 'Një bllok për kurorë'}
        )

        RecipeItem.objects.get_or_create(
            product=crown_zirconia,
            material=ceramic_powder,
            defaults={'quantity': 5.0, 'notes': 'Pluhur për shtresën estetike'}
        )

        RecipeItem.objects.get_or_create(
            product=bridge_3unit,
            material=zirconia_block,
            defaults={'quantity': 2.0, 'notes': 'Dy blloqe për urë 3 njësi'}
        )

        RecipeItem.objects.get_or_create(
            product=bridge_3unit,
            material=ceramic_powder,
            defaults={'quantity': 15.0, 'notes': 'Pluhur për shtresën estetike'}
        )

        self.stdout.write('Created products and materials')

    def create_demo_cases(self, clinic_profile, tech_profile):
        """Create demo cases."""
        crown_product = FinalProduct.objects.get(name='Kurorë Zirkoni Estetike')
        bridge_product = FinalProduct.objects.get(name='Urë 3 Njësi Zirkoni')

        # Case 1: In Design
        Case.objects.get_or_create(
            case_id='LAB-25-07-0001',
            defaults={
                'clinic': clinic_profile,
                'patient_name': 'Fatmir Ymeri',
                'product': crown_product,
                'status': 'IN_DESIGN',
                'creation_type': 'DIGITAL',
                'priority': 'NORMAL',
                'assigned_technician': tech_profile,
                'due_date': date.today() + timedelta(days=7),
                'color_shade': 'A2',
                'special_instructions': 'Kujdes i veçantë në anatomia e premolarit',
            }
        )

        # Case 2: Pending Approval
        Case.objects.get_or_create(
            case_id='LAB-25-07-0002',
            defaults={
                'clinic': clinic_profile,
                'patient_name': 'Ana Koci',
                'product': bridge_product,
                'status': 'PENDING_APPROVAL',
                'creation_type': 'PHYSICAL',
                'priority': 'HIGH',
                'assigned_technician': tech_profile,
                'due_date': date.today() + timedelta(days=5),
                'color_shade': 'A3',
                'special_instructions': 'Urë e fiksuar nga 14-16',
            }
        )

        # Case 3: In Production
        Case.objects.get_or_create(
            case_id='LAB-25-07-0003',
            defaults={
                'clinic': clinic_profile,
                'patient_name': 'Genti Hoxha',
                'product': crown_product,
                'status': 'IN_PRODUCTION',
                'creation_type': 'DIGITAL',
                'priority': 'URGENT',
                'assigned_technician': tech_profile,
                'due_date': date.today() + timedelta(days=2),
                'color_shade': 'A1',
                'special_instructions': 'Pacient i ri, kujdes në kontaktet',
            }
        )

        # Case 4: Pending Pickup
        Case.objects.get_or_create(
            case_id='LAB-25-07-0004',
            defaults={
                'clinic': clinic_profile,
                'patient_name': 'Elira Spahiu',
                'product': crown_product,
                'status': 'PENDING_PICKUP',
                'creation_type': 'PHYSICAL',
                'priority': 'NORMAL',
                'due_date': date.today() + timedelta(days=10),
                'color_shade': 'B2',
                'special_instructions': 'Marrja e impressionit në klinikë',
            }
        )

        self.stdout.write('Created demo cases')
