# Generated by Django 4.2.7 on 2025-07-17 11:51

from decimal import Decimal
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("users", "0001_initial"),
        ("cases", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="Invoice",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "invoice_number",
                    models.CharField(
                        max_length=20, unique=True, verbose_name="Numri i Faturës"
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("DRAFT", "Draft"),
                            ("SENT", "Dërguar"),
                            ("PAID", "Paguar"),
                            ("OVERDUE", "Me Vonesë"),
                            ("CANCELLED", "Anuluar"),
                        ],
                        default="DRAFT",
                        max_length=20,
                        verbose_name="Statusi",
                    ),
                ),
                (
                    "issue_date",
                    models.DateField(
                        auto_now_add=True, verbose_name="Data e Nxjerrjes"
                    ),
                ),
                ("due_date", models.DateField(verbose_name="Data e Scadencës")),
                (
                    "total_amount",
                    models.DecimalField(
                        decimal_places=2,
                        default=Decimal("0.00"),
                        max_digits=10,
                        validators=[
                            django.core.validators.MinValueValidator(Decimal("0.00"))
                        ],
                        verbose_name="Totali",
                    ),
                ),
                (
                    "tax_amount",
                    models.DecimalField(
                        decimal_places=2,
                        default=Decimal("0.00"),
                        max_digits=10,
                        validators=[
                            django.core.validators.MinValueValidator(Decimal("0.00"))
                        ],
                        verbose_name="Tatimi",
                    ),
                ),
                ("notes", models.TextField(blank=True, verbose_name="Shënime")),
                (
                    "pdf_file",
                    models.FileField(
                        blank=True,
                        null=True,
                        upload_to="invoices/%Y/%m/",
                        verbose_name="PDF Faturë",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Krijuar më"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Përditësuar më"),
                ),
                (
                    "clinic",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="invoices",
                        to="users.clinicprofile",
                        verbose_name="Klinika",
                    ),
                ),
            ],
            options={
                "verbose_name": "Faturë",
                "verbose_name_plural": "Faturat",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="Payment",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "amount",
                    models.DecimalField(
                        decimal_places=2,
                        max_digits=10,
                        validators=[
                            django.core.validators.MinValueValidator(Decimal("0.01"))
                        ],
                        verbose_name="Shuma",
                    ),
                ),
                (
                    "payment_method",
                    models.CharField(
                        choices=[
                            ("CASH", "Kesh"),
                            ("BANK_TRANSFER", "Transfertë Bankare"),
                            ("CARD", "Kartë"),
                            ("CHECK", "Çek"),
                            ("OTHER", "Tjetër"),
                        ],
                        max_length=20,
                        verbose_name="Mënyra e Pagesës",
                    ),
                ),
                (
                    "reference_number",
                    models.CharField(
                        blank=True, max_length=100, verbose_name="Numri i Referencës"
                    ),
                ),
                ("payment_date", models.DateField(verbose_name="Data e Pagesës")),
                ("notes", models.TextField(blank=True, verbose_name="Shënime")),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Krijuar më"),
                ),
                (
                    "invoice",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="payments",
                        to="billing.invoice",
                        verbose_name="Fatura",
                    ),
                ),
            ],
            options={
                "verbose_name": "Pagesë",
                "verbose_name_plural": "Pagesat",
                "ordering": ["-payment_date"],
            },
        ),
        migrations.CreateModel(
            name="InvoiceItem",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "description",
                    models.CharField(max_length=255, verbose_name="Përshkrimi"),
                ),
                (
                    "quantity",
                    models.IntegerField(
                        default=1,
                        validators=[django.core.validators.MinValueValidator(1)],
                        verbose_name="Sasia",
                    ),
                ),
                (
                    "unit_price",
                    models.DecimalField(
                        decimal_places=2,
                        max_digits=10,
                        validators=[
                            django.core.validators.MinValueValidator(Decimal("0.01"))
                        ],
                        verbose_name="Çmimi për Njësi",
                    ),
                ),
                (
                    "amount",
                    models.DecimalField(
                        decimal_places=2,
                        max_digits=10,
                        validators=[
                            django.core.validators.MinValueValidator(Decimal("0.01"))
                        ],
                        verbose_name="Shuma",
                    ),
                ),
                (
                    "case",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="invoice_items",
                        to="cases.case",
                        verbose_name="Porosia",
                    ),
                ),
                (
                    "invoice",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="items",
                        to="billing.invoice",
                        verbose_name="Fatura",
                    ),
                ),
            ],
            options={
                "verbose_name": "Artikulli i Faturës",
                "verbose_name_plural": "Artikujt e Faturës",
                "ordering": ["id"],
            },
        ),
    ]
