from rest_framework import status, viewsets
from rest_framework.decorators import action, api_view, permission_classes
from rest_framework.permissions import IsA<PERSON>enticated, AllowAny
from rest_framework.response import Response
from rest_framework.views import APIView
from django.contrib.auth import login, logout
from django.contrib.auth.decorators import login_required
from django.shortcuts import render
from .models import CustomUser, ClinicProfile, TechnicianProfile
from .serializers import (
    UserRegistrationSerializer, UserLoginSerializer, UserProfileSerializer,
    PasswordChangeSerializer
)
from .permissions import IsAdminOrManager


class UserRegistrationView(APIView):
    """
    User registration view.
    Allows clinic registration with admin approval.
    """
    permission_classes = [AllowAny]
    
    def post(self, request):
        """Register a new clinic user."""
        serializer = UserRegistrationSerializer(data=request.data)
        
        if serializer.is_valid():
            user = serializer.save()
            return Response({
                'message': 'Registration successful. Please wait for admin approval.',
                'user_id': user.id,
                'email': user.email
            }, status=status.HTTP_201_CREATED)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class UserLoginView(APIView):
    """
    User login view.
    Authenticates user and creates session.
    """
    permission_classes = [AllowAny]
    
    def post(self, request):
        """Authenticate user and create session."""
        serializer = UserLoginSerializer(data=request.data, context={'request': request})
        
        if serializer.is_valid():
            user = serializer.validated_data['user']
            login(request, user)
            
            # Return user profile data
            profile_serializer = UserProfileSerializer(user)
            return Response({
                'message': 'Login successful',
                'user': profile_serializer.data
            }, status=status.HTTP_200_OK)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class UserLogoutView(APIView):
    """
    User logout view.
    Destroys user session.
    """
    permission_classes = [IsAuthenticated]
    
    def post(self, request):
        """Logout user and destroy session."""
        logout(request)
        return Response({
            'message': 'Logout successful'
        }, status=status.HTTP_200_OK)


class UserProfileView(APIView):
    """
    User profile management view.
    """
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        """Get current user profile."""
        serializer = UserProfileSerializer(request.user)
        return Response(serializer.data)
    
    def patch(self, request):
        """Update user profile."""
        serializer = UserProfileSerializer(
            request.user, 
            data=request.data, 
            partial=True
        )
        
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class PasswordChangeView(APIView):
    """
    Password change view.
    """
    permission_classes = [IsAuthenticated]
    
    def post(self, request):
        """Change user password."""
        serializer = PasswordChangeSerializer(
            data=request.data,
            context={'request': request}
        )
        
        if serializer.is_valid():
            serializer.save()
            return Response({
                'message': 'Password changed successfully'
            }, status=status.HTTP_200_OK)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class UserManagementViewSet(viewsets.ModelViewSet):
    """
    User management viewset for admins.
    """
    queryset = CustomUser.objects.all()
    serializer_class = UserProfileSerializer
    permission_classes = [IsAuthenticated, IsAdminOrManager]
    
    def get_queryset(self):
        """Filter users based on query parameters."""
        queryset = CustomUser.objects.select_related('clinic_profile', 'technician_profile')
        
        # Filter by role
        role = self.request.query_params.get('role')
        if role:
            queryset = queryset.filter(role=role)
        
        # Filter by active status
        active = self.request.query_params.get('active')
        if active is not None:
            queryset = queryset.filter(is_active=active.lower() == 'true')
        
        return queryset.order_by('-date_joined')
    
    @action(detail=True, methods=['post'])
    def activate(self, request, pk=None):
        """Activate user account."""
        user = self.get_object()
        user.is_active = True
        user.save()
        
        return Response({
            'message': f'User {user.email} has been activated',
            'user': UserProfileSerializer(user).data
        })
    
    @action(detail=True, methods=['post'])
    def deactivate(self, request, pk=None):
        """Deactivate user account."""
        user = self.get_object()
        user.is_active = False
        user.save()
        
        return Response({
            'message': f'User {user.email} has been deactivated',
            'user': UserProfileSerializer(user).data
        })
    
    @action(detail=False, methods=['get'])
    def pending_approvals(self, request):
        """Get users pending approval."""
        pending_users = CustomUser.objects.filter(
            is_active=False,
            role='CLIENT'
        ).select_related('clinic_profile')
        
        serializer = UserProfileSerializer(pending_users, many=True)
        return Response({
            'count': pending_users.count(),
            'users': serializer.data
        })


@api_view(['GET'])
@permission_classes([AllowAny])
def auth_status(request):
    """Check authentication status."""
    if request.user.is_authenticated:
        serializer = UserProfileSerializer(request.user)
        return Response({
            'authenticated': True,
            'user': serializer.data
        })
    else:
        return Response({
            'authenticated': False,
            'user': None
        })


def login_page(request):
    """Login page view."""
    return render(request, 'auth/login.html')


def register_page(request):
    """Registration page view."""
    return render(request, 'auth/register.html')


@login_required
def profile_page(request):
    """Profile page view."""
    return render(request, 'auth/profile.html')
