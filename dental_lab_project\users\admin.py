from django.contrib import admin
from django.contrib.auth.admin import UserAdmin
from django.contrib.auth.forms import User<PERSON>reationForm, UserChangeForm
from django.utils.translation import gettext_lazy as _
from .models import CustomUser, ClinicProfile, TechnicianProfile


class CustomUserCreationForm(UserCreationForm):
    """Custom user creation form for admin."""
    
    class Meta:
        model = CustomUser
        fields = ('email', 'role')


class CustomUserChangeForm(UserChangeForm):
    """Custom user change form for admin."""
    
    class Meta:
        model = CustomUser
        fields = ('email', 'role', 'is_active')


class ClinicProfileInline(admin.StackedInline):
    """Inline admin for clinic profile."""
    model = ClinicProfile
    can_delete = False
    verbose_name_plural = 'Profili i Klinikës'
    
    def get_queryset(self, request):
        """Only show for users with CLIENT role."""
        return super().get_queryset(request).filter(user__role='CLIENT')


class TechnicianProfileInline(admin.StackedInline):
    """Inline admin for technician profile."""
    model = TechnicianProfile
    can_delete = False
    verbose_name_plural = 'Profili i Teknikut'
    
    def get_queryset(self, request):
        """Only show for users with TECHNICIAN role."""
        return super().get_queryset(request).filter(user__role='TECHNICIAN')


@admin.register(CustomUser)
class CustomUserAdmin(UserAdmin):
    """Custom user admin with inline profiles."""
    
    form = CustomUserChangeForm
    add_form = CustomUserCreationForm
    model = CustomUser
    
    list_display = ('email', 'first_name', 'last_name', 'role', 'is_active', 'date_joined')
    list_filter = ('role', 'is_active', 'date_joined')
    search_fields = ('email', 'first_name', 'last_name')
    ordering = ('email',)
    
    fieldsets = (
        (None, {'fields': ('email', 'password')}),
        (_('Personal info'), {'fields': ('first_name', 'last_name')}),
        (_('Permissions'), {
            'fields': ('role', 'is_active', 'is_staff', 'is_superuser', 'groups', 'user_permissions'),
        }),
        (_('Important dates'), {'fields': ('last_login', 'date_joined')}),
    )
    
    add_fieldsets = (
        (None, {
            'classes': ('wide',),
            'fields': ('email', 'password1', 'password2', 'role'),
        }),
    )
    
    def get_inlines(self, request, obj):
        """Dynamically add inlines based on user role."""
        inlines = []
        if obj:
            if obj.role == 'CLIENT':
                inlines.append(ClinicProfileInline)
            elif obj.role == 'TECHNICIAN':
                inlines.append(TechnicianProfileInline)
        return inlines


@admin.register(ClinicProfile)
class ClinicProfileAdmin(admin.ModelAdmin):
    """Admin for clinic profiles."""
    
    list_display = ('clinic_name', 'nipt', 'contact_person', 'phone_number')
    search_fields = ('clinic_name', 'nipt', 'contact_person')
    readonly_fields = ('user',)
    
    def get_queryset(self, request):
        """Only show clinic profiles."""
        return super().get_queryset(request).select_related('user')


@admin.register(TechnicianProfile)
class TechnicianProfileAdmin(admin.ModelAdmin):
    """Admin for technician profiles."""
    
    list_display = ('user', 'specialization')
    search_fields = ('user__first_name', 'user__last_name', 'specialization')
    readonly_fields = ('user',)
    
    def get_queryset(self, request):
        """Only show technician profiles."""
        return super().get_queryset(request).select_related('user')
